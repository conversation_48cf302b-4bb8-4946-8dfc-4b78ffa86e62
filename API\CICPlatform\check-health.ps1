# PowerShell script to check health of all CIC Platform services
Write-Host "Checking CIC Platform Services Health..." -ForegroundColor Green

$services = @(
    @{ Name = "API Gateway"; Url = "http://localhost:5004/health" },
    @{ Name = "Admin API"; Url = "http://localhost:5002/health" },
    @{ Name = "CIC API"; Url = "http://localhost:5003/health" }
)

$allHealthy = $true

foreach ($service in $services) {
    try {
        Write-Host "Checking $($service.Name)..." -NoNewline
        $response = Invoke-RestMethod -Uri $service.Url -Method Get -TimeoutSec 10

        if ($response.status -eq "Healthy") {
            Write-Host " ✓ Healthy" -ForegroundColor Green
        } else {
            Write-Host " ✗ Unhealthy ($($response.status))" -ForegroundColor Red
            $allHealthy = $false
        }
    }
    catch {
        Write-Host " ✗ Not accessible" -ForegroundColor Red
        Write-Host "    Error: $($_.Exception.Message)" -ForegroundColor DarkRed
        $allHealthy = $false
    }
}

Write-Host ""
if ($allHealthy) {
    Write-Host "All services are healthy! ✓" -ForegroundColor Green
    Write-Host ""
    Write-Host "Access points:" -ForegroundColor Cyan
    Write-Host "  - API Gateway Swagger: http://localhost:5004/swagger" -ForegroundColor White
    Write-Host "  - Admin API Swagger: http://localhost:5002/swagger" -ForegroundColor White
    Write-Host "  - CIC API Swagger: http://localhost:5003/swagger" -ForegroundColor White
} else {
    Write-Host "Some services are not healthy! ✗" -ForegroundColor Red
    Write-Host "Run 'docker-compose logs [service-name]' to check logs" -ForegroundColor Yellow
}
