﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration
{
    public sealed class MenuConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<Menu>(contextFilter), IEntityConfiguration
    {
        public MenuConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<Menu> builder)
        {
            builder.ToTable("MenuConfig");
            builder.ToTable(t => t.HasComment("The application's menus are kept in this table."));
            builder.HasKey(e => e.MenuId);
            builder.Property(e => e.MenuId).ValueGeneratedNever();
            builder.Property(e => e.Title)
                .HasAnnotation("DisplayName", "Title")
                .IsRequired()
                .HasMaxLength(100)
                .HasComment("This field is used to provide the menu's title. When the user clicks on a module in the menu tree, this is the name that appears.");
            builder.Property(e => e.MenuType)
                .IsRequired()
                .HasComment("This field is used to indicate the type of menu, such as reports, transactions, master, etc.");
            builder.Property(e => e.URL)
                .IsRequired(false)
                .HasMaxLength(2000)
                .HasComment("The framework engine can be redirected to external URLs using this field.");
            builder.Property(e => e.Remarks)
                .IsRequired(false)
                .HasMaxLength(500)
                .HasAnnotation("DisplayName", "Remarks")
                .HasComment("This field is used to give a detailed explanation of the menu that is created for.");
            builder.Property(e => e.Path)
                .IsRequired(false)
                .HasMaxLength(2000)
                .HasAnnotation("DisplayName", "Path")
                .HasComment("This field is used to dynamically route to the appropriate APIs.");
            builder.Property(e => e.Icon)
                .IsRequired(false)
                .HasMaxLength(50)
                .HasAnnotation("DisplayName", "Icon")
                .HasComment("This field is used to give the menu a distinctive icon.");
            builder.Property(e => e.Color)
                .IsRequired(false)
                .HasMaxLength(50)
                .HasAnnotation("DisplayName", "Color")
                .HasComment("This field is used to give the menu a distinctive color.");
            builder.Property(e => e.ComponentName)
                .IsRequired(false)
                .HasMaxLength(100)
                .HasAnnotation("DisplayName", "Component Name")
                .HasComment("This field is used to give the menu's component name");
            builder.Property(e => e.ParamSchema)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Parameter Schema")
                .HasComment("");

            builder.Property(e => e.BusinessUnitFilterLevel)
                .IsRequired(false);
            builder.Property(e => e.ServiceType)
                .IsRequired(false);
            builder.Property(e => e.ServiceSchemaId)
                .IsRequired(false);
            builder.Property(e => e.MenuCode)
                .IsRequired(false)
                .HasMaxLength(6)
                .HasAnnotation("DisplayName", "Menu Code")
                .HasComment("If the same path is used for several menus, this field is utilized to provide a unique code for the menu to appropriately route the framework engine.");
            builder.Property(e => e.ServiceParamJson)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Service Param Json");
            builder.Property(e => e.Tooltip)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Tooltip");
            builder.Property(e => e.SortOrder)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Sort Order")
                .HasComment("The order in which the menus are populated in the menu tree is defined by this field.");

            builder.HasOne(a => a.Module).WithMany().HasForeignKey(a => a.ModuleId);
            builder.Navigation(e => e.Module).AutoInclude();
            builder.Property(e => e.ModuleId)
                .HasAnnotation("DisplayName", "Module")
                .HasAnnotation("AuditVirtualModel", "Module")
                .HasAnnotation("DisplayMember", "Name")
                .HasComment("The module that the menu is made for is specified in this field.");

            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<Menu>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new MenuConfiguration(contextFilter, feedSeedData));
        }
    }
}