﻿using CICPlatform.Admin.Application.Contracts;
using CICPlatform.Admin.Application.DTOs.User;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Admin.Infrastructure.Persistence.Repositories
{
    public class UserRepository(AdminDbContext adminDbContext) : GenericRepository<User>(adminDbContext), IUserRepository
    {
        public async Task<List<UserDTO>> GetAllUsers()
        {
            var query = (from user in adminDbContext.User.AsNoTracking()
                         select new UserDTO
                         {
                             UserId = user.UserId,
                             UserName = user.UserName,
                             LoginName = user.LoginName,
                             EffectiveFrom = user.EffectiveFrom,
                             EffectiveTo = user.EffectiveTo,
                             Password = user.Password,
                             Email = user.Email,
                             UserImage = user.UserImage,
                             IsLocked = user.IsLocked,
                             Remarks = user.Remarks,
                             FirstName = user.FirstName,
                             MiddleName = user.MiddleName,
                             LastName = user.LastName,
                             PwdUpdatedDate = user.PwdUpdatedDate,
                             FirstLogin = user.FirstLogin,
                             ConfirmPassword = user.ConfirmPassword,
                             Active = user.Active,
                             DefaultBusinessUnitId = user.DefaultBusinessUnitId,
                             DefaultRoleId = user.DefaultRoleId
                         });

            return await query.ToListAsync();
        }
    }
}
