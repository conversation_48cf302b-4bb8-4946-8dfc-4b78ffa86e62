﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>eb86402a-7109-4fd1-b96b-7548b7e4e9cb</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CICPlatform.Application\CICPlatform.Application.csproj" />
    <ProjectReference Include="..\CICPlatform.Infrastructure\CICPlatform.Infrastructure.csproj" />
  </ItemGroup>

	<Target Name="DeletePDBFiles" AfterTargets="Publish">
		<ItemGroup>
			<FilesToDelete Include="$(PublishDir)*.pdb" />
		</ItemGroup>
		<Delete Files="@(FilesToDelete)">
			<Output TaskParameter="DeletedFiles" ItemName="FilesDeleted" />
		</Delete>
		<Message Text="Deleted PDB files: @(FilesDeleted)" Importance="high" />
	</Target>
	<Target Name="DeleteAppSettingsFiles" AfterTargets="Publish">
		<ItemGroup>
			<FilesToDelete Include="$(PublishDir)appsettings*.json" Exclude="$(PublishDir)appsettings.json;$(PublishDir)appsettings.$(Mode).json" />
		</ItemGroup>
		<Delete Files="@(FilesToDelete)">
			<Output TaskParameter="DeletedFiles" ItemName="FilesDeleted" />
		</Delete>
		<Message Text="Deleted appsettings files: @(FilesDeleted)" Importance="high" />
	</Target>
	
</Project>
