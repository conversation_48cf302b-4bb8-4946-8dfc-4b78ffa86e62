﻿using CICPlatform.Admin.Application.Commands;
using CICPlatform.Admin.Application.DTOs.User;
using CICPlatform.Admin.Application.Models;
using CICPlatform.Admin.Application.Queries;
using MediatR;
using System.Net;
using static CICPlatform.Admin.Domain.Shared.Enums.SharedEnums;

namespace CICPlatform.Admin.API.EndpointDefinitions
{
    public static class UserEndpointDefinition
    {
        public static void RegisterEndpoints(WebApplication app)
        {
            var userRouteGroup = app.MapGroup("/api/v1/users")
                .WithTags("users")
                .RequireAuthorization();

            userRouteGroup.MapGet("/", GetAllUsers)
                .WithName("GetUsers")
                .Produces<APIResponse>(200, "application/json");

            userRouteGroup.MapGet("/{id}", GetUserById)
                .WithName("GetUser")
                .Produces<APIResponse>(200, "application/json").Produces(400);

            userRouteGroup.MapPost("/", CreateUser)
                .WithName("CreateUsers")
                .Produces<APIResponse>(200).Produces(400);

            userRouteGroup.MapPut("/", UpdateUser)
                .WithName("UpdateUsers")
                .Accepts<UpdateUserDto>(ApplicationJsonType)
                .Produces<APIResponse>(200).Produces(400);

            userRouteGroup.MapDelete("/{id}", DeleteUser)
                .WithName("DeleteUsers")
                .Produces<APIResponse>(200).Produces(400);
        }

        /// <summary>
        /// Get all users
        /// </summary>
        /// <param name="mediator"></param>
        /// <returns></returns>
        private static async Task<IResult> GetAllUsers(IMediator mediator)
        {
            APIResponse resultObj = new();
            var userList = await mediator.Send(new GetAllUserRequest());

            resultObj.IsSuccess = true;
            resultObj.Result = userList;
            resultObj.StatusCode = HttpStatusCode.OK;
            return TypedResults.Ok(resultObj);
        }

        /// <summary>
        /// Get announcemnet by passing Id
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        private static async Task<IResult> GetUserById(IMediator mediator, int id)
        {
            APIResponse resultObj = new();

            var user = await mediator.Send(new GetUserRequest { Id = id });

            if (user is not null)
            {
                resultObj.IsSuccess = true;
                resultObj.Result = user;
                resultObj.StatusCode = HttpStatusCode.OK;
                return TypedResults.Ok(resultObj);
            }
            return TypedResults.BadRequest(resultObj);
        }

        /// <summary>
        /// Get announcemnet by passing Id
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="id"></param>
        /// <returns></returns>

        public static async Task<IResult> CreateUser(IMediator mediator, CreateUserDto createUserDto)
        {
            var command = new CreateUserCommand { CreateUserDto = createUserDto };
            APIResponse resultObj = new();
            var user = await mediator.Send(command);
            resultObj.IsSuccess = true;
            resultObj.Result = user;
            resultObj.StatusCode = HttpStatusCode.OK;

            return TypedResults.Ok(resultObj);
        }

        /// <summary>
        /// Update user
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="updateUserDto"></param>
        /// <returns></returns>
        private static async Task<IResult> UpdateUser(IMediator mediator, UpdateUserDto updateUserDto)
        {
            var command = new UpdateUserCommand { UpdateUserDto = updateUserDto };
            APIResponse resultObj = new();
            var user = await mediator.Send(command);

            resultObj.IsSuccess = true;
            resultObj.Result = user;
            resultObj.StatusCode = HttpStatusCode.OK;

            return TypedResults.Ok(resultObj);
        }

        /// <summary>
        /// Delete user
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        private static async Task<IResult> DeleteUser(IMediator mediator, int Id)
        {
            var command = new DeleteUserCommand { Id = Id };
            APIResponse resultObj = new();
            var user = await mediator.Send(command);

            resultObj.IsSuccess = true;
            resultObj.Result = user;
            resultObj.StatusCode = HttpStatusCode.OK;

            return TypedResults.Ok(resultObj);
        }
    }
}