﻿namespace CICPlatform.Admin.Domain.System
{
    public class UserDetail
    {
        public int UserDetailId { get; set; }
        public int UserId { get; set; }
        public virtual User User { get; set; }
        public int? MaximumLoginAttempts { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public DateTime? LastLogoutDate { get; set; }
        public string? LastLoginIp { get; set; }
        public string? FavouriteMenu { get; set; }
        public string? FrequentMenu { get; set; }
        public short? Active { get; set; }
        public short? IsSystemDefined { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}