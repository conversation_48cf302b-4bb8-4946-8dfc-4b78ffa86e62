﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public sealed class ModuleGroupConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<ModuleGroup>(contextFilter), IEntityConfiguration
    {
        public ModuleGroupConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<ModuleGroup> builder)
        {
            builder.ToTable("ModuleGroup");
            builder.ToTable(t => t.HasComment("This table is used to define the application's available module groups. A module group consists of several connected submodules."));
            builder.HasKey(e => e.ModuleGroupId);
            builder.Property(e => e.ModuleGroupId).ValueGeneratedNever();
            builder.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(10)
                .HasComment("This field is used to give the module group a unique code.");
            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .HasComment("This field is used to give the module group a distinctive name.");
            builder.Property(e => e.Description)
                .IsRequired(false)
                .HasMaxLength(200)
                .HasComment("The purpose of this field is to give a detailed description of the module group.");
            builder.Property(e => e.SortOrder)
                .IsRequired(false)
                .HasComment("This field is used to specify the sequence in which module groups appear in menus and tiles.");
            builder.Property(e => e.Color)
                .IsRequired(false)
                .HasMaxLength(50)
                .HasComment("This field is used to give the module group a distinctive color.");
            builder.Property(e => e.Icon)
                .IsRequired(false)
                .HasMaxLength(50)
                .HasComment("This field is used to give the module group a distinctive icon.");
            builder.Property(e => e.DashboardURL)
                .IsRequired(false)
                .HasMaxLength(2000)
                .HasComment("This field allows you to specify the dashboard's URL that will be displayed once the user clicks on the module group tile. If a user clicks on a dashboard report for the human resource module group, for example, the report will be landed directly in the layout of the application.");

            builder
                .HasMany(e => e.Module)
                .WithOne(e => e.ModuleGroup)
                .HasForeignKey(e => e.ModuleGroupId)
                .HasPrincipalKey(e => e.ModuleGroupId);

            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<ModuleGroup>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new ModuleGroupConfiguration(contextFilter, feedSeedData));
        }
    }
}