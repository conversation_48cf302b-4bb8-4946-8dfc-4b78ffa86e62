﻿namespace CICPlatform.Domain
{
    public class PFLoanDetail
    {
        public int LoanNumber { get; set; }
        public int? LoanTypeId { get; set; }
        public virtual LoanType LoanType { get; set; }
        public string LoanSource { get; set; }
        public DateTime EndDate { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal InterestRate { get; set; }
        public int PaymentPeriod { get; set; }
        public bool IsScheduleInterest { get; set; }
        public bool IsScheduleRepayment { get; set; }
        public int? CityId { get; set; }
        public virtual City City { get; set; }
        public virtual ICollection<PFLoanInterestRateSchedule> PFLoanInterestRateSchedules { get; set; }
        public virtual ICollection<PFLoanRepaymentSchedule> PFLoanRepaymentSchedules { get; set; }
    }
}