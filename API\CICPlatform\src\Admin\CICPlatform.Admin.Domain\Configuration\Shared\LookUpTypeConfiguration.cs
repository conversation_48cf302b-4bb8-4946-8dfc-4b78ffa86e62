﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public sealed class LookUpTypeConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<LookUpType>(contextFilter), IEntityConfiguration
    {
        public LookUpTypeConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<LookUpType> builder)
        {
            builder.ToTable("LookUpType");
            builder.ToTable(t => t.HasComment("The application's necessary system control lookup types are defined on this table. The majority of lookup types are provided as seed data."));
            builder.HasKey(e => e.LookUpTypeId);
            builder.Property(e => e.LookUpTypeId).ValueGeneratedNever();
            builder.Property(e => e.Field<PERSON>)
                .HasAnnotation("DisplayName", "Name")
                .IsRequired()
                .HasMaxLength(50).HasComment("This field is used to give the lookup type a distinctive name.");
            builder.Property(e => e.Description)
                .HasAnnotation("DisplayName", "Description")
                .IsRequired(false)
                .HasMaxLength(50)
                .HasComment("This field serves to give a thorough explanation of the lookup type used for.");
            builder
                .HasMany(e => e.LookUpInfo)
                .WithOne(e => e.LookUpType)
                .HasForeignKey(e => e.LookUpTypeId)
                .HasPrincipalKey(e => e.LookUpTypeId);
            builder.Property(e => e.BusinessUnitId)
                .HasAnnotation("DisplayName", "Business Unit")
                .IsRequired(false)
                .HasComment("This field maps the business unit to the appropriate lookup types associated with.");

            builder.HasOne(a => a.Module).WithMany().HasForeignKey(a => a.ModuleId);
            builder.Navigation(e => e.Module).AutoInclude();
            builder.Property(e => e.ModuleId)
                .HasAnnotation("DisplayName", "Module")
                .HasAnnotation("AuditVirtualModel", "Module")
                .HasAnnotation("DisplayMember", "Name")
                .HasComment("Module-wise lookup types are maintained using this field. It will be simple for the user to filter the lookup types by module.");
            builder.Property(e => e.Active).HasAnnotation("DisplayName", "Active");
            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<LookUpType>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new LookUpTypeConfiguration(contextFilter, feedSeedData));
        }
    }
}