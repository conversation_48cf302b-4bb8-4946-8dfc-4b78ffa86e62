﻿using AutoMapper;
using CICPlatform.Admin.Application.Commands;
using CICPlatform.Admin.Application.Contracts;
using CICPlatform.Admin.Application.DTOs.User;
using CICPlatform.Admin.Domain.System;
using MediatR;

namespace CICPlatform.Admin.Application.CommandHandlers.User
{
    public class CreateUserCommandHandler(IUnitOfWork unitOfWork, IMapper mapper, IMediator mediator) : IRequestHandler<CreateUserCommand, UserDTO>
    {
        public async Task<UserDTO> Handle(CreateUserCommand request, CancellationToken cancellationToken)
        {
            var user = mapper.Map<Admin.Domain.System.User>(request.CreateUserDto);
            var userLists = await unitOfWork.UserRepository.Add(user);
            await unitOfWork.UserDetailRepository.Add(new UserDetail() { UserId = request.CreateUserDto.UserId });
            await unitOfWork.Save();
            return mapper.Map<UserDTO>(userLists);
        }
    }
}