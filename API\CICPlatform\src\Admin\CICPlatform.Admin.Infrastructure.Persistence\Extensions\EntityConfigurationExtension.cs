﻿using CICPlatform.Admin.Domain.Shared;
using Microsoft.EntityFrameworkCore;
using CICPlatform.Admin.Domain.Configuration;
using CICPlatform.Admin.Domain.Shared.Contracts;

namespace CICPlatform.Admin.Infrastructure.Persistence.Extensions
{
    public static class EntityConfigurationExtension
    {
        public static void RegisterModels(this ModelBuilder modelBuilder, DbContextFilter contextFilter)
        {
            var adminEntityConfigurations = typeof(AssemblyReference).Assembly
                .GetTypes()
                .Where(t => t.IsAssignableTo(typeof(IEntityConfiguration)) && !t.IsAbstract && !t.IsInterface)
                .Select(Activator.CreateInstance)
                .Cast<IEntityConfiguration>();
            foreach (var entity in adminEntityConfigurations)
            {
                entity.ConfigureEntity(modelBuilder, contextFilter, false);
            }

            var sharedEntityConfigurations = typeof(AssemblyReference).Assembly
                .GetTypes()
                .Where(t => t.IsAssignableTo(typeof(IEntityConfiguration)) && !t.IsAbstract && !t.IsInterface)
                .Select(Activator.CreateInstance)
                .Cast<IEntityConfiguration>();
            foreach (var entity in sharedEntityConfigurations)
            {
                entity.ConfigureEntity(modelBuilder, contextFilter, false);
            }
        }
    }
}