﻿namespace CICPlatform.Admin.Domain.Shared.Enums
{
    public static class SharedEnums
    {
        public const string ApplicationJsonType = "application/json";
        public const string TenantSetting = "Tenant";
        public const string DefaultLocale = "en-IN";

        public enum PermissionOperator
        {
            And = 1, Or = 2
        }

        public static class AuthorizationMode
        {
            public const string RoleBased = "Role";
            public const string PermissionBased = "Permission";
        }

        public enum PermissionMode
        {
            RoleBased = 1,
            PermissionBased = 2
        }

        public static class Permissions
        {
            public const string Create = "A";
            public const string Update = "E";
            public const string Delete = "D";
            public const string View = "V";
        }

        public static class DefaultDatabase
        {
            public const string MySql = "MySQL";
            public const string PostgreSql = "PostgreSQL";
            public const string MSSql = "SQLServer";
        }

        public enum ApplicationUser
        {
            Admin = 1,
        }
    }
}