﻿using CICPlatform.Application.DTOs;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Application.Commands
{
    public class CreateRevenueIncomeTypeCommand : IRequest<RevenueIncomeTypeDto>
    {
        public CreateRevenueIncomeTypeDto CreateRevenueIncomeTypeDto { get; set; }

        public CreateRevenueIncomeTypeCommand()
        {
            CreateRevenueIncomeTypeDto = new CreateRevenueIncomeTypeDto();
        }
    }
}
