﻿namespace CICPlatform.Admin.Domain.Shared
{
    public class LookUpType : AuditAttributes
    {
        public int LookUpTypeId { get; set; }
        public int ModuleId { get; set; }
        public virtual Module Module { get; set; }
        public int? BusinessUnitId { get; set; }
        public string FieldName { get; set; }
        public string? Description { get; set; }
        public short? IsSystemDefined { get; set; }
        public ICollection<LookUpInfo> LookUpInfo { get; set; }
    }
}