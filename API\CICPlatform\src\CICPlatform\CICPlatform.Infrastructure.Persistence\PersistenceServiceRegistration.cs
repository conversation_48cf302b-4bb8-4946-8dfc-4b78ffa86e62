﻿using CICPlatform.Application.Contracts;
using CICPlatform.Infrastructure.Persistence.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace CICPlatform.Infrastructure.Persistence
{
    public static class PersistenceServiceRegistration
    {
        public static IServiceCollection AddPersistenceServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<CICApplicationDbContext>(options =>
                options.UseNpgsql(configuration.GetConnectionString("Default"), npgsqlOptions =>
                npgsqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "cic_data")));

            services.AddScoped<IUnitOfWork, UnitOfWork>();
            return services;
        }
    }
}
