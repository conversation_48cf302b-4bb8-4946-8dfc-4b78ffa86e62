﻿namespace CICPlatform.Admin.Domain.System
{
    public class UserInfoDetail
    {
        public string UserName { get; set; }
        public string? LoginName { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public string? Password { get; set; }
        public int? BusinessUnitId { get; set; }
        public string UserEmail { get; set; }
        public string? UserImage { get; set; }
        public bool? Active { get; set; }
        public bool? IsLocked { get; set; }
        public int UserId { get; set; }
        public int? MaximumLoginAttempts { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public DateTime? LastLogoutDate { get; set; }
        public string? LastLoginIp { get; set; }
        public DateTime? PwdUpdatedDate { get; set; }
        public bool? FirstLogin { get; set; }
        public string? FavouriteMenu { get; set; }
        public string? FrequentMenu { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}