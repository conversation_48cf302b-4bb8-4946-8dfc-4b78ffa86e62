# PowerShell script to test individual services
param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("admin-api", "cic-api", "api-gateway", "all")]
    [string]$Service = "all"
)

Write-Host "Testing CIC Platform Services..." -ForegroundColor Green

function Test-Service {
    param($ServiceName, $Port)
    
    Write-Host "Testing $ServiceName..." -ForegroundColor Yellow
    
    try {
        # Build and start the service
        Write-Host "  Building and starting $ServiceName..." -NoNewline
        docker-compose up --build -d $ServiceName
        Write-Host " ✓" -ForegroundColor Green
        
        # Wait for service to start
        Write-Host "  Waiting for service to be ready..." -NoNewline
        Start-Sleep 10
        Write-Host " ✓" -ForegroundColor Green
        
        # Test health endpoint
        Write-Host "  Testing health endpoint..." -NoNewline
        $response = Invoke-RestMethod -Uri "http://localhost:$Port/health" -Method Get -TimeoutSec 10
        if ($response.status -eq "Healthy") {
            Write-Host " ✓ Healthy" -ForegroundColor Green
        } else {
            Write-Host " ✗ Unhealthy" -ForegroundColor Red
        }
        
        # Test Swagger endpoint
        Write-Host "  Testing Swagger endpoint..." -NoNewline
        $swaggerResponse = Invoke-WebRequest -Uri "http://localhost:$Port/swagger" -Method Get -TimeoutSec 10
        if ($swaggerResponse.StatusCode -eq 200) {
            Write-Host " ✓ Accessible" -ForegroundColor Green
            Write-Host "    Swagger UI: http://localhost:$Port/swagger" -ForegroundColor Cyan
        } else {
            Write-Host " ✗ Not accessible" -ForegroundColor Red
        }
        
    } catch {
        Write-Host " ✗ Error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "    Check logs: docker-compose logs $ServiceName" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# Test services based on selection
switch ($Service) {
    "admin-api" {
        Test-Service "admin-api" 5002
    }
    "cic-api" {
        Test-Service "cic-api" 5003
    }
    "api-gateway" {
        Test-Service "api-gateway" 5004
    }
    "all" {
        Test-Service "admin-api" 5002
        Test-Service "cic-api" 5003
        Test-Service "api-gateway" 5004
    }
}

Write-Host "Testing complete!" -ForegroundColor Green
Write-Host "Note: Make sure Identity Server is running from Visual Studio on port 5130" -ForegroundColor Yellow
