# PowerShell script to restart services with database connection fix
Write-Host "Restarting services with database connection fix..." -ForegroundColor Green

# Stop all containers
Write-Host "Stopping all containers..." -ForegroundColor Yellow
docker-compose down

# Start infrastructure first
Write-Host "Starting infrastructure (PostgreSQL + Redis)..." -ForegroundColor Cyan
docker-compose up --build postgres redis -d

# Wait for infrastructure to be ready
Write-Host "Waiting for infrastructure to be ready..." -ForegroundColor Cyan
Start-Sleep 20

# Check if postgres is ready
Write-Host "Checking PostgreSQL connection..." -ForegroundColor Cyan
$maxAttempts = 10
$attempt = 1
do {
    try {
        $result = docker-compose exec postgres pg_isready -U techskill -d techskill
        if ($result -like "*accepting connections*") {
            Write-Host "PostgreSQL is ready!" -ForegroundColor Green
            break
        }
    }
    catch {
        Write-Host "Attempt $attempt/$maxAttempts - PostgreSQL not ready yet..." -ForegroundColor Yellow
    }
    Start-Sleep 3
    $attempt++
} while ($attempt -le $maxAttempts)

# Start APIs with rebuild
Write-Host "Starting APIs with updated configuration..." -ForegroundColor Cyan
docker-compose up --build admin-api cic-api -d

# Wait for APIs to start
Write-Host "Waiting for APIs to start..." -ForegroundColor Cyan
Start-Sleep 25

# Start gateway
Write-Host "Starting API Gateway..." -ForegroundColor Cyan
docker-compose up --build api-gateway -d

# Wait for gateway
Write-Host "Waiting for gateway to start..." -ForegroundColor Cyan
Start-Sleep 15

# Check health
Write-Host "Checking service health..." -ForegroundColor Green
.\check-health.ps1

Write-Host ""
Write-Host "Database connection fix applied!" -ForegroundColor Green
Write-Host "Services should now connect to the containerized PostgreSQL database" -ForegroundColor Yellow
Write-Host ""
Write-Host "Access points:" -ForegroundColor Cyan
Write-Host "  - API Gateway: http://localhost:5004/swagger" -ForegroundColor White
Write-Host "  - Admin API: http://localhost:5002/swagger" -ForegroundColor White
Write-Host "  - CIC API: http://localhost:5003/swagger" -ForegroundColor White
Write-Host ""
Write-Host "Test the CIC API via gateway: http://localhost:5004/cic-gate/v1/revenueincomes" -ForegroundColor Cyan
