﻿using CICPlatform.Admin.Domain.Shared;

namespace CICPlatform.Admin.Domain.System
{
    public class User :  AuditAttributes
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string? LoginName { get; set; }
        public int? DefaultBusinessUnitId { get; set; }
        public int? DefaultRoleId { get; set; }
        public int? AuthenticationSettingsId { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public string? Password { get; set; }
        public string Email { get; set; }
        public string? UserImage { get; set; }
        public int? IsLocked { get; set; }
        public string? Remarks { get; set; }
        public string? FirstName { get; set; }
        public string? MiddleName { get; set; }
        public string? LastName { get; set; }
        public DateTime? PwdUpdatedDate { get; set; }
        public short? FirstLogin { get; set; }
        public string ConfirmPassword { get; set; }
        public string? OldPassword { get; set; }
    }
}