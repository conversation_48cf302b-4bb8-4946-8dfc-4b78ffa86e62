﻿using AutoMapper;
using CICPlatform.Admin.Application.Contracts;
using CICPlatform.Admin.Application.DTOs.User;
using CICPlatform.Admin.Application.Queries;
using MediatR;

namespace CICPlatform.Admin.Application.QueryHandlers.User
{
    public class GetAllUserRequestHandler(IUnitOfWork unitOfWork, IMapper mapper) : IRequestHandler<GetAllUserRequest, List<UserDTO>>
    {
        public async Task<List<UserDTO>> Handle(GetAllUserRequest request, CancellationToken cancellationToken)
        {
            return await unitOfWork.UserRepository.GetAllUsers();
        }
    }
}