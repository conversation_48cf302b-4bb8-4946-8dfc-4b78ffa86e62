{"GlobalConfiguration": {"BaseUrl": "http://api-gateway:5004", "ServiceDiscoveryProvider": {"Type": "AppConfiguration"}, "RateLimitOptions": {"DisableRateLimitHeaders": false, "QuotaExceededMessage": "API rate limit exceeded", "HttpStatusCode": 429}, "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 3, "DurationOfBreak": 10000, "TimeoutValue": 5000}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": true}, "DownstreamHttpVersion": "2.0"}}