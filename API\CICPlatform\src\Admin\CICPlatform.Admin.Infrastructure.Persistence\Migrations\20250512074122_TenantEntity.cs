﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CICPlatform.Admin.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class TenantEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "UserInfo",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "UserGroupMapping",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "UserGroup",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "Permission",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "ModuleGroup",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "Module",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "MenuConfig",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "LookUpType",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TenantId",
                schema: "CIC",
                table: "LookUpInfo",
                type: "integer",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "UserInfo");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "UserGroupMapping");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "UserGroup");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "Permission");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "ModuleGroup");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "Module");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "MenuConfig");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "LookUpType");

            migrationBuilder.DropColumn(
                name: "TenantId",
                schema: "CIC",
                table: "LookUpInfo");
        }
    }
}
