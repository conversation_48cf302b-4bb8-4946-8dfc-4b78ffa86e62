﻿// <auto-generated />
using System;
using CICPlatform.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CICPlatform.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(CICApplicationDbContext))]
    partial class CICApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("cic_data")
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CICPlatform.Domain.Craft.PFRevenueIncome", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<short>("Active")
                        .HasColumnType("smallint");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("RevenueIncomeTypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("TypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<decimal>("Value")
                        .HasColumnType("numeric");

                    b.Property<string>("Year")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RevenueIncomeTypeId");

                    b.ToTable("PFRevenueIncome", "cic_data");
                });

            modelBuilder.Entity("CICPlatform.Domain.Craft.PFRevenueIncomeType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<short>("Active")
                        .HasColumnType("smallint");

                    b.Property<int?>("CityId")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Formula")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GuidanceShortTerm")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer");

                    b.Property<int>("RevenueIncomeTypeId")
                        .HasColumnType("integer");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("RevenueIncomeTypeId");

                    b.ToTable("PFRevenueIncomeType", "cic_data");
                });

            modelBuilder.Entity("CICPlatform.Domain.Craft.PFRevenueIncome", b =>
                {
                    b.HasOne("CICPlatform.Domain.Craft.PFRevenueIncomeType", "RevenueIncomeType")
                        .WithMany("PFRevenueIncomes")
                        .HasForeignKey("RevenueIncomeTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RevenueIncomeType");
                });

            modelBuilder.Entity("CICPlatform.Domain.Craft.PFRevenueIncomeType", b =>
                {
                    b.HasOne("CICPlatform.Domain.Craft.PFRevenueIncomeType", "RevenueIncomeType")
                        .WithMany()
                        .HasForeignKey("RevenueIncomeTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RevenueIncomeType");
                });

            modelBuilder.Entity("CICPlatform.Domain.Craft.PFRevenueIncomeType", b =>
                {
                    b.Navigation("PFRevenueIncomes");
                });
#pragma warning restore 612, 618
        }
    }
}
