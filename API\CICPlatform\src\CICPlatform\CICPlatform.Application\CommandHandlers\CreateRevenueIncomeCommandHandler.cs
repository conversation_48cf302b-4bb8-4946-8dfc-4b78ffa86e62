﻿using AutoMapper;
using CICPlatform.Application.Commands;
using CICPlatform.Application.Contracts;
using CICPlatform.Application.DTOs;
using CICPlatform.Domain.Craft;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Application.CommandHandlers
{
    public class CreateRevenueIncomeCommandHandler(IUnitOfWork unitOfWork, IMapper mapper, IMediator mediator) : IRequestHandler<CreateRevenueIncomeCommand, RevenueIncomeDto>
    {
        public async Task<RevenueIncomeDto> Handle(CreateRevenueIncomeCommand request, CancellationToken cancellationToken)
        {
            var revenueIncome = mapper.Map<PFRevenueIncome>(request.CreateRevenueIncomeDto);
            var revenueIncomeLists = await unitOfWork.RevenueIncomeRepository.Add(revenueIncome);
            await unitOfWork.Save();
            return mapper.Map<RevenueIncomeDto>(revenueIncomeLists);
        }
    }
}
