﻿using Duende.IdentityModel.Client;
using Microsoft.AspNetCore.Mvc;

namespace IdentityServer.Controllers
{
    [Route("api/token")]
    [ApiController]
    public class TokenController : ControllerBase
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<TokenController> _logger;
        private readonly IConfiguration _configuration;

        public TokenController(
            IHttpClientFactory httpClientFactory,
            ILogger<TokenController> logger,
            IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        [HttpGet]
        public async Task<IActionResult> GenerateToken()
        {
            try
            {
                // Create HttpClient using factory
                var httpClient = _httpClientFactory.CreateClient("IdentityServer");

                // Get the server's base address from the current request or configuration
                var baseAddress = _configuration["IdentityServer:BaseUrl"] ?? $"{Request.Scheme}://{Request.Host.Value}";

                // Get client credentials from configuration or use defaults
                var clientId = _configuration["IdentityServer:ClientId"] ?? "ClientId";
                var clientSecret = _configuration["IdentityServer:ClientSecret"] ?? "ClientSecret";
                var scopes = _configuration.GetSection("IdentityServer:Scope").Get<string[]>();

                var scopeString = string.Join(" ", scopes);

                _logger.LogInformation("Requesting token for client {ClientId} with scope {Scope}", clientId, scopes);

                var tokenResponse = await httpClient.RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest
                {
                    Address = $"{baseAddress}/connect/token",
                    ClientId = clientId,
                    ClientSecret = clientSecret,
                    Scope = scopeString
                });

                if (tokenResponse.IsError)
                {
                    _logger.LogWarning("Token request failed: {Error}, {Description}",
                        tokenResponse.Error, tokenResponse.ErrorDescription);

                    return BadRequest(new { error = tokenResponse.Error, description = tokenResponse.ErrorDescription });
                }

                _logger.LogInformation("Token generated successfully for client {ClientId}", clientId);
                return Ok(tokenResponse.Json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while generating token");
                return StatusCode(500, new { error = "Internal server error", description = "An error occurred while processing your request." });
            }
        }
    }
}