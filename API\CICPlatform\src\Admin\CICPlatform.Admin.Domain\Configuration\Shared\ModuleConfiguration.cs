﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public sealed class ModuleConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<Module>(contextFilter), IEntityConfiguration
    {
        public ModuleConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<Module> builder)
        {
            builder.ToTable("Module");
            builder.ToTable(t => t.HasComment("The purpose of this table is to define a module inside a particular module group that the application offers."));
            builder.HasKey(e => e.ModuleId);
            builder.Property(e => e.ModuleId).ValueGeneratedNever();
            builder.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(10)
                .HasComment("This field is used for providing a unique code for the module.");
            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .HasComment("This field is used to give the module a distinctive name.");
            builder.Property(e => e.Description)
                .IsRequired(false)
                .HasMaxLength(200)
                .HasComment("This field is used to give a detailed explanation of what the module is meant for.");
            builder.Property(e => e.SortOrder)
                .IsRequired(false)
                .HasComment("The order in which the modules are populated in the menu tree is defined by this field.");
            builder.Property(e => e.Color)
                .IsRequired(false)
                .HasMaxLength(50)
                .HasComment("This field is used to give the module a distinctive color.");
            builder.Property(e => e.Icon)
                .IsRequired(false)
                .HasMaxLength(50)
                .HasComment("This field is used to give the module a distinctive icon.");
            builder.Property(e => e.DashboardURL)
                .IsRequired(false)
                .HasMaxLength(2000)
                .HasComment("The dashboard's URL is specified in this field in case the user clicks on a module in the menu tree. For instance, the payroll module dashboard report will be displayed immediately if the user clicks on payroll root menu.");

            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<Module>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new ModuleConfiguration(contextFilter, feedSeedData));
        }
    }
}