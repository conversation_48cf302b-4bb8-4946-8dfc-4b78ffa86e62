﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public sealed class SchemaDefinitionConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : IEntityTypeConfiguration<SchemaDefinition>, IEntityConfiguration
    {
        public SchemaDefinitionConfiguration() : this(new DbContextFilter())
        {
        }

        public void Configure(EntityTypeBuilder<SchemaDefinition> builder)
        {
            builder.ToTable("SchemaDefinition");
            builder.ToTable(t => t.HasComment("The application's JSON schemas are kept in this table."));
            builder.HasKey(e => e.SchemaDefinitionId);
            builder.Property(e => e.SchemaDefinitionId).ValueGeneratedNever();
            builder.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(5)
                .HasComment("This field is used for providing a unique code for the schema.");
            builder.Property(e => e.SchemaName)
                .IsRequired()
                .HasMaxLength(200)
                .HasComment("This field is used for providing a unique name for the schema.");
            builder.Property(e => e.Description)
                .IsRequired()
                .HasMaxLength(1000)
                .HasComment("This field is used to give a detailed explanation of what the schema is meant for.");
            builder.Property(e => e.DomainSchemaFieldMapping)
                .IsRequired(false);
            builder.Property(e => e.SchemaStructure)
                .IsRequired()
                .HasComment("This field is used to give the JSON structure of the schema is used for.");

            builder.Property(e => e.OriginBusinessUnitId)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Origin BusinessUnit");
            builder.Property(e => e.ModelDefinitionId).IsRequired(false);
            builder.Property(e => e.IsSystemDefined).IsRequired(false);

            builder.HasQueryFilter(c => c.Active != -1);

            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<SchemaDefinition>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new SchemaDefinitionConfiguration(contextFilter, feedSeedData));
        }
    }
}