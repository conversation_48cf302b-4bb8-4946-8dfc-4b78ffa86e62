﻿using AutoMapper;
using CICPlatform.Application.Commands;
using CICPlatform.Application.Contracts;
using CICPlatform.Application.DTOs;
using CICPlatform.Domain.Craft;
using MediatR;

namespace CICPlatform.Application.CommandHandlers
{
    public class CreateRevenueIncomeTypeCommandHandler(IUnitOfWork unitOfWork, IMapper mapper, IMediator mediator) : IRequestHandler<CreateRevenueIncomeTypeCommand, RevenueIncomeTypeDto>
    {
        public async Task<RevenueIncomeTypeDto> Handle(CreateRevenueIncomeTypeCommand request, CancellationToken cancellationToken)
        {
            var revenueIncomeType = mapper.Map<PFRevenueIncomeType>(request.CreateRevenueIncomeTypeDto);
            var revenueIncomeTypeLists = await unitOfWork.RevenueIncomeTypeRepository.Add(revenueIncomeType);
            await unitOfWork.Save();
            return mapper.Map<RevenueIncomeTypeDto>(revenueIncomeTypeLists);
        }
    }
}
