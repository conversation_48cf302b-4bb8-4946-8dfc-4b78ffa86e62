﻿
using CICPlatform.Domain.Contracts;
using CICPlatform.Domain.Craft;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;


namespace CICPlatform.Domain.Configuration
{
    public sealed class PFRevenueIncomeTypeConfiguration(bool feedSeedData = false) : GeneralAttributeConfiguration<PFRevenueIncomeType>(), IEntityConfiguration
    {
        public override void Configure(EntityTypeBuilder<PFRevenueIncomeType> builder)
        {
            builder.ToTable("RevenueIncomeType");
            builder.Property(p => p.Description).IsRequired().HasMaxLength(200);
            builder.Property(p => p.Code).IsRequired().HasMaxLength(50);
            builder.Property(p => p.Formula).HasMaxLength(255);
            builder.Property(p => p.GuidanceShortTerm).HasMaxLength(500);
            builder.Property(p => p.Level).IsRequired();
            builder.Property(p => p.SortOrder).IsRequired();

            // Define Relationships
            builder.HasOne(p => p.RevenueIncomeType)
                   .WithMany()
                   .HasForeignKey(p => p.ParentId)
                   .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete

            builder.HasQueryFilter(c => c.Active != -1);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new PFRevenueIncomeConfiguration());
        }

    }
}
