﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration
{
    public sealed class UserConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<User>(contextFilter), IEntityConfiguration
    {
        public UserConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<User> builder)
        {
            builder.ToTable("UserInfo");
            builder.ToTable(t => t.HasComment("The purpose of this table is to define the application users."));
            builder.HasKey(c => c.UserId);
            builder.Property(c => c.UserId).ValueGeneratedNever();
            builder.Property(c => c.UserName)
                .IsRequired()
                .HasMaxLength(100)
                .HasAnnotation("DisplayName", "User Name")
                .HasComment("A user can enter a legitimate user name in this field. Once the user logs into the application, this user name will appear in the account details.");
            builder.Property(c => c.LoginName)
                .IsRequired(false)
                .HasMaxLength(100)
                .HasAnnotation("DisplayName", "Login Name")
                .HasComment("This field is used to give the user a unique login name.");
            builder.Property(c => c.Password)
                .HasAnnotation("DisplayName", "Password")
                .IsRequired(false)
                .HasMaxLength(50)
                .HasComment("This field is used to set the user's password. This password would be encrypted.");
            builder.Property(c => c.EffectiveFrom)
                .HasAnnotation("DisplayName", "Effective From")
                .HasComment("This option is used to specify the date that the user can start using the application.");
            builder.Property(c => c.EffectiveTo)
                .HasAnnotation("DisplayName", "Effectie Date")
                .HasComment("The end date that the user can access the program is set in this field. If we do not wish to restrict with an end date, this date can be left blank.");
            builder.Property(c => c.Email)
                .HasAnnotation("DisplayName", "User Email")
                .HasMaxLength(100).HasComment("The user's unique email address is defined in this field.");
            builder.Property(c => c.Active)
                .HasAnnotation("DisplayName", "Active");
            builder.Property(c => c.Remarks)
                .HasAnnotation("DisplayName", "Remarks")
                .IsRequired(false)
                .HasMaxLength(500);
            builder.Property(c => c.FirstName)
                .HasAnnotation("DisplayName", "First Name")
                .IsRequired(false)
                .HasMaxLength(50);
            builder.Property(c => c.MiddleName)
                .HasAnnotation("DisplayName", "Middle Name")
                .IsRequired(false)
                .HasMaxLength(50);
            builder.Property(c => c.LastName)
                .HasAnnotation("DisplayName", "Last Name")
                .IsRequired(false)
                .HasMaxLength(50);
            builder.Property(c => c.OldPassword)
                .HasAnnotation("DisplayName", "Old Password")
                .IsRequired(false).HasMaxLength(500);
            builder.Property(c => c.DefaultBusinessUnitId)
                .HasAnnotation("DisplayName", "Default Business Unit")
                .IsRequired(false)
                .HasComment("For various reasons, a user may have access to several accounting locations within the application. The business unit that would be the user's primary default location is specified in this field. When a user logs in, this accounting location will be selected as their primary accounting location by default.");
            builder.Property(c => c.DefaultRoleId)
                .HasAnnotation("DisplayName", "Default Role")
                .IsRequired(false)
                .HasComment("A user may have several roles in an application for various objectives. His primary role within his permissible roles is mapped using this field.");
            builder.Property(c => c.AuthenticationSettingsId)
                .IsRequired(false);
            builder.Ignore(c => c.ConfirmPassword);

            builder.HasQueryFilter(c => c.Active != -1 && (contextFilter.UserId != 1 ? c.UserId != 1 : true));

            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<User>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new UserConfiguration(contextFilter, feedSeedData));
        }
    }
}