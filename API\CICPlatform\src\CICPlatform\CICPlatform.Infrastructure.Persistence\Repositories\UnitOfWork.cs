﻿using CICPlatform.Application.Contracts;

namespace CICPlatform.Infrastructure.Persistence.Repositories
{
    public class UnitOfWork(CICApplicationDbContext CICapplicationDbContext) : IUnitOfWork
    {

        private IRevenueIncomeTypeRepository _revenueIncomeTypeRepository;
        public IRevenueIncomeTypeRepository RevenueIncomeTypeRepository => _revenueIncomeTypeRepository ?? new RevenueIncomeTypeRepository(CICapplicationDbContext);
        
        private IRevenueIncomeRepository _revenueIncomeRepository;
        public IRevenueIncomeRepository RevenueIncomeRepository => _revenueIncomeRepository ?? new RevenueIncomeRepository(CICapplicationDbContext);

        public void Dispose()
        {
            CICapplicationDbContext.Dispose();
            GC.SuppressFinalize(this);
        }

        public async Task Save()
        {
            await CICapplicationDbContext.SaveChangesAsync();
        }
    }
}
