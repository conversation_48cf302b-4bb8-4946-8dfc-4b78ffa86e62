﻿using AutoMapper;
using CICPlatform.Admin.Application.DTOs.User;
using CICPlatform.Admin.Domain.System;

namespace CICPlatform.Admin.Application.Profiles
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<User, UserDTO>().ReverseMap();
            CreateMap<User, CreateUserDto>().ReverseMap();
            CreateMap<User, UpdateUserDto>().ReverseMap();
        }
    }
}