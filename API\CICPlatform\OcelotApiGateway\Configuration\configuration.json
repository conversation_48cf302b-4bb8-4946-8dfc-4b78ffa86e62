{"GlobalConfiguration": {"BaseUrl": "http://localhost:5000", "ServiceDiscoveryProvider": {"Type": "AppConfiguration"}, "RequestIdKey": "OcRequestId", "RateLimitOptions": {"EnableRateLimiting": true, "HttpStatusCode": 429, "ClientIdHeader": "ClientId", "QuotaExceededMessage": "Too many requests. Please try again later.", "RateLimitCounterPrefix": "ocelot_limit"}, "DownstreamScheme": "http"}, "Routes": [{"DownstreamPathTemplate": "/swagger/{everything}", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5273}], "UpstreamPathTemplate": "/gateway/swagger/{everything}", "UpstreamHttpMethod": ["GET"], "SwaggerKey": "users"}, {"DownstreamPathTemplate": "/api/{version}/{everything}", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5273}], "UpstreamPathTemplate": "/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": []}, "RateLimitRule": {"EnableRateLimiting": true, "Period": "1m", "Limit": 10}, "LoadBalancerOptions": {"Type": "RoundR<PERSON>in"}, "RouteClaimsRequirement": {"scope": "OcelotGateway"}}]}