﻿using CICPlatform.Admin.Application.Extensions;
using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.System;
using CICPlatform.Admin.Infrastructure.Persistence.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;
using static CICPlatform.Admin.Domain.Shared.Enums.SharedEnums;

namespace CICPlatform.Admin.Infrastructure.Persistence
{
    public class AdminDbContext : DbContext
    {
        private readonly DbContextFilter contextFilter;
        private readonly TenantEntity _tenantContext;
        public string DataBase
        {
            get;
            private set;
        }

        public AdminDbContext(DbContextOptions options, DbContextFilter contextFilter, IConfiguration configuration, TenantEntity tenantContext) : base(options)
        {
            contextFilter = contextFilter ?? throw new ArgumentNullException(nameof(contextFilter));
            _tenantContext = tenantContext;

            this.contextFilter = contextFilter;
            DataBase = configuration.GetSection("DatabaseSettings:DefaultDB").IsValid();
            switch (DataBase)
            {
                case DefaultDatabase.PostgreSql:
                    AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
                    break;

                default:
                    break;
            }
        }

        public DbSet<AuditLog> AuditLog { get; set; }
        public DbSet<User> User { get; set; }
        public DbSet<SchemaDefinition> SchemaDefinition { get; set; }
        public DbSet<Template> Template { get; set; }
        public DbSet<Menu> Menu { get; set; }
        public DbSet<ModuleGroup> ModuleGroup { get; set; }
        public DbSet<Module> Module { get; set; }
        public DbSet<LookUpType> LookUpType { get; set; }
        public DbSet<LookUpInfo> LookUpInfo { get; set; }
        public DbSet<Permission> Permission { get; set; }
        public DbSet<Log> Log { get; set; }
        public DbSet<UserDetail> UserDetail { get; set; }
        public DbSet<UserInfoDetail> UserInfoDetail { get; set; }
        public DbSet<UserGroup> UserGroup { get; set; }
        public DbSet<UserGroupMapping> UserGroupMapping { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.RegisterModels(contextFilter);
            modelBuilder.Entity<User>().HasQueryFilter(u => u.TenantId == _tenantContext.TenantId);            

            switch (DataBase)
            {
                case DefaultDatabase.MySql:
                    break;

                case DefaultDatabase.MSSql:
                default:
                    modelBuilder.HasDefaultSchema("CIC");
                    break;
            }
            base.OnModelCreating(modelBuilder);
        }
    }
}