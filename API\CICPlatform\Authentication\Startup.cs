﻿using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;

namespace IdentityServer
{
    public class Startup
    {
        private readonly IWebHostEnvironment _environment;

        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Configuration = configuration;
            _environment = environment;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();

            // Add proper logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                // Add Application Insights or other logging providers
            });

            // Add health checks
            services.AddHealthChecks()
                .AddCheck("self", () => HealthCheckResult.Healthy());

            // Add CORS with specific origins
            services.AddCors(options =>
            {
                options.AddPolicy("DefaultPolicy", policy =>
                {
                    policy.WithOrigins(Configuration.GetSection("AllowedOrigins").Get<string[]>() ?? new[] { "https://localhost:5004" })
                          .WithMethods("GET", "POST")
                          .WithHeaders("Authorization", "Content-Type")
                          .AllowCredentials();
                });
            });

            // Add HttpClientFactory instead of scoped HttpClient
            services.AddHttpClient();

            // Configure IdentityServer
            var identityBuilder = services.AddIdentityServer(options =>
            {
                options.Events.RaiseErrorEvents = true;
                options.Events.RaiseInformationEvents = true;
                options.Events.RaiseFailureEvents = true;
                options.Events.RaiseSuccessEvents = true;

                // Add more secure options
                options.EmitStaticAudienceClaim = false;
                options.Authentication.CookieLifetime = TimeSpan.FromHours(2);
                options.Authentication.CookieSlidingExpiration = true;
            });

            // For production, use a certificate
            if (_environment.IsDevelopment())
            {
                identityBuilder.AddDeveloperSigningCredential();
            }
            else
            {
                try
                {
                    // Try to load certificate from store or file
                    var certPath = Configuration["Certificate:Path"];
                    var certPassword = Configuration["Certificate:Password"];

                    if (!string.IsNullOrEmpty(certPath) && !string.IsNullOrEmpty(certPassword))
                    {
                        var cert = new X509Certificate2(
                            Path.Combine(_environment.ContentRootPath, certPath),
                            certPassword);
                        identityBuilder.AddSigningCredential(cert);
                    }
                    else
                    {
                        // Fallback to developer credential with warning
                        identityBuilder.AddDeveloperSigningCredential();
                        var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Startup>>();
                        logger.LogWarning("Using developer signing credential in production environment. This is not secure!");
                    }
                }
                catch (Exception ex)
                {
                    var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Startup>>();
                    logger.LogError(ex, "Error loading certificate. Falling back to developer signing credential.");
                    identityBuilder.AddDeveloperSigningCredential();
                }
            }

            // For production, use database storage
            if (_environment.IsDevelopment() || !Configuration.GetValue<bool>("UseDatabase"))
            {
                identityBuilder
                    .AddInMemoryApiScopes(Config.ApiScopes)
                    .AddInMemoryApiResources(Config.Apis)
                    .AddInMemoryClients(Config.Clients);
                //// Add a test user for resource owner password flow
                //identityBuilder.AddTestUsers(new List<Duende.IdentityServer.Test.TestUser>
                //{
                //    new Duende.IdentityServer.Test.TestUser
                //    {
                //        SubjectId = "test-user",
                //        Username = "test",
                //        Password = "test",
                //        Claims = new List<Claim>
                //        {
                //            new Claim("role", "Admin")
                //        }
                //    }
                //});
            }
            else
            {
                // Use Entity Framework stores
                var connectionString = Configuration.GetConnectionString("IdentityServer");
                var migrationsAssembly = typeof(Startup).GetTypeInfo().Assembly.GetName().Name;

                if (!string.IsNullOrEmpty(connectionString))
                {
                    identityBuilder
                        .AddConfigurationStore(options =>
                        {
                            options.ConfigureDbContext = builder =>
                                builder.UseNpgsql(connectionString,
                                    sql => sql.MigrationsAssembly(migrationsAssembly));
                        })
                        .AddOperationalStore(options =>
                        {
                            options.ConfigureDbContext = builder =>
                                builder.UseNpgsql(connectionString,
                                    sql => sql.MigrationsAssembly(migrationsAssembly));
                            options.EnableTokenCleanup = true;
                            options.TokenCleanupInterval = 3600; // seconds
                        });
                }
                else
                {
                    var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Startup>>();
                    logger.LogWarning("Database connection string not found. Falling back to in-memory stores.");

                    identityBuilder
                        .AddInMemoryApiScopes(Config.ApiScopes)
                        .AddInMemoryApiResources(Config.Apis)
                        .AddInMemoryClients(Config.Clients);

                    //// Add a test user for resource owner password flow
                    //identityBuilder.AddTestUsers(new List<Duende.IdentityServer.Test.TestUser>
                    //{
                    //    new Duende.IdentityServer.Test.TestUser
                    //    {
                    //        SubjectId = "test-user",
                    //        Username = "test",
                    //        Password = "test",
                    //        Claims = new List<Claim>
                    //        {
                    //            new Claim("role", "Admin")
                    //        }
                    //    }
                    //});
                }
            }
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {
            try
            {
                if (env.IsDevelopment())
                {
                    app.UseDeveloperExceptionPage();
                }
                else
                {
                    app.UseExceptionHandler("/Error");
                    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                    app.UseHsts();
                }

                // Add health checks endpoint
                app.UseHealthChecks("/health", new HealthCheckOptions
                {
                    ResponseWriter = async (context, report) =>
                    {
                        context.Response.ContentType = "application/json";
                        var result = System.Text.Json.JsonSerializer.Serialize(
                            new
                            {
                                status = report.Status.ToString(),
                                checks = report.Entries.Select(e => new
                                {
                                    name = e.Key,
                                    status = e.Value.Status.ToString(),
                                    description = e.Value.Description
                                })
                            });
                        await context.Response.WriteAsync(result);
                    }
                });

                // HttpsRedirection should come before routing
                app.UseHttpsRedirection();
                app.UseRouting();

                // IdentityServer middleware
                app.UseIdentityServer();

                // Use CORS with named policy
                app.UseCors("DefaultPolicy");

                app.UseEndpoints(endpoints =>
                {
                    endpoints.MapControllers();
                    endpoints.MapHealthChecks("/health");
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred during application startup");
                throw;
            }
        }
    }
}