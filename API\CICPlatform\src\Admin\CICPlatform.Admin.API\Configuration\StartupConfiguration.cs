using CICPlatform.Admin.Application;
using CICPlatform.Admin.Application.Contracts;
using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Infrastructure.Persistence;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Security.Claims;

namespace CICPlatform.Admin.API.Configuration
{
    /// <summary>
    /// Handles application startup configuration
    /// </summary>
    public static class StartupConfiguration
    {
        /// <summary>
        /// Configures the application builder
        /// </summary>
        public static WebApplicationBuilder ConfigureBuilder(this WebApplicationBuilder builder)
        {
            // Use environment variables for URLs (set by Docker or local environment)
            var urls = builder.Configuration["ASPNETCORE_URLS"];
            if (!string.IsNullOrEmpty(urls))
            {
                builder.WebHost.UseUrls(urls);
            }

            // Add environment-specific configuration
            var env = builder.Environment.EnvironmentName;
            builder.Configuration.AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true);

            return builder;
        }

        /// <summary>
        /// Configures the services for the application
        /// </summary>
        public static WebApplicationBuilder ConfigureServices(this WebApplicationBuilder builder)
        {
            // Add services to the container
            builder.Services.AddEndpointsApiExplorer();

            // Configure JWT Bearer authentication with Azure AD
            ConfigureAuthentication(builder);

            // Add authorization policies
            ConfigureAuthorization(builder);

            // Add CORS
            ConfigureCors(builder);

            // Add Swagger with environment-specific configuration
            ConfigureSwagger(builder);

            // Add application and infrastructure services
            builder.Services.ConfigureApplicationService();
            builder.Services.AddPersistenceServices(builder.Configuration);

            // Register services
            builder.Services.AddScoped<DbContextFilter>();
            builder.Services.AddScoped<TenantEntity>();

            return builder;
        }

        /// <summary>
        /// Configures the HTTP request pipeline
        /// </summary>
        public static WebApplication ConfigureApp(this WebApplication app)
        {
            var logger = app.Services.GetRequiredService<ILogger<Program>>();

            try
            {
                // Configure the HTTP request pipeline based on environment
                var developmentSettings = app.Configuration.GetSection("Development");
                var securitySettings = app.Configuration.GetSection("Security");

                if (app.Environment.IsDevelopment())
                {
                    if (developmentSettings.GetValue<bool>("EnableDeveloperExceptionPage", true))
                    {
                        app.UseDeveloperExceptionPage();
                    }

                    if (developmentSettings.GetValue<bool>("EnableSwagger", true))
                    {
                        app.UseSwagger();
                        app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Admin Web API v1"));
                    }
                }
                else
                {
                    app.UseExceptionHandler("/Error");
                    if (securitySettings.GetValue<bool>("RequireHttps", true))
                    {
                        app.UseHsts();
                    }
                }

                // Use HTTPS redirection based on security settings
                if (securitySettings.GetValue<bool>("RequireHttps", !app.Environment.IsDevelopment()))
                {
                    app.UseHttpsRedirection();
                }

                app.UseRouting();

                // Add CORS middleware
                app.UseCors("DefaultPolicy");

                // Add authentication and authorization middleware
                app.UseAuthentication();
                app.UseAuthorization();

                // Add tenant middleware
                app.UseMiddleware<TenantMiddleware>();

                logger.LogInformation("Application startup complete");
            }
            catch (Exception ex)
            {
                // Log the exception properly
                logger.LogError(ex, "An error occurred during application startup");
                // Don't throw the exception, just log it
            }

            return app;
        }

        #region Private Helper Methods

        private static void ConfigureAuthentication(WebApplicationBuilder builder)
        {
            // Configure JWT Bearer authentication with Azure AD
            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                // Get configuration from appsettings.json
                var jwtSettings = builder.Configuration.GetSection("JwtSettings");
                var azureAd = builder.Configuration.GetSection("AzureAd");

                // Get security settings
                var securitySettings = builder.Configuration.GetSection("Security");

                options.Authority = $"{azureAd["Instance"]}{azureAd["TenantId"]}";
                options.Audience = azureAd["Audience"];
                options.RequireHttpsMetadata = securitySettings.GetValue<bool>("RequireHttps", !builder.Environment.IsDevelopment());

                // Configure token validation parameters with environment-specific settings
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = jwtSettings.GetValue<bool>("ValidateIssuer", true),
                    ValidateAudience = jwtSettings.GetValue<bool>("ValidateAudience", !builder.Environment.IsDevelopment()),
                    ValidateLifetime = jwtSettings.GetValue<bool>("ValidateLifetime", true),
                    ValidateIssuerSigningKey = jwtSettings.GetValue<bool>("ValidateIssuerSigningKey", true),

                    ValidIssuer = jwtSettings["ValidIssuer"],

                    ValidAudiences = [azureAd["Audience"]],

                    ClockSkew = TimeSpan.Parse(jwtSettings["ClockSkew"] ?? "00:05:00"), // Environment-specific clock skew

                    NameClaimType = "name",
                    RoleClaimType = "roles" // Azure AD standard for roles
                };

                // Add comprehensive logging for debugging
                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                        logger.LogWarning("Authentication failed: {Exception}", context.Exception);
                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context =>
                    {
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                        logger.LogInformation("Token validated successfully");

                        // Log all claims in the token
                        if (context.Principal?.Identity is ClaimsIdentity identity)
                        {
                            logger.LogInformation("Claims in the token:");
                            foreach (var claim in identity.Claims)
                            {
                                logger.LogInformation("Claim: {type} = {value}", claim.Type, claim.Value);
                            }
                        }

                        return Task.CompletedTask;
                    },
                    OnMessageReceived = context =>
                    {
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                        if (!string.IsNullOrEmpty(context.Token))
                        {
                            logger.LogInformation("JWT token received");
                        }
                        return Task.CompletedTask;
                    }
                };
            });
        }

        private static void ConfigureAuthorization(WebApplicationBuilder builder)
        {
            // Add authorization policies
            builder.Services.AddAuthorization(options =>
            {
                // Admin access policy - extremely permissive for development
                //options.AddPolicy(Policies.AdminAccess, policy =>
                //{
                //    // For development, allow any authenticated user
                //    policy.RequireAuthenticatedUser();
                //});

                //// CIC admin access policy - requires CICAdmin role
                //options.AddPolicy(Policies.CICAdminAccess, policy =>
                //{
                //    policy.RequireRole(Roles.CICAdmin);
                //});

                //// User access policy - requires authenticated user
                //options.AddPolicy(Policies.UserAccess, policy =>
                //{
                //    policy.RequireAuthenticatedUser();
                //});
            });
        }

        private static void ConfigureCors(WebApplicationBuilder builder)
        {
            // Add CORS
            builder.Services.AddCors(options =>
            {
                options.AddPolicy("DefaultPolicy", policy =>
                {
                    policy.WithOrigins(builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:5004" })
                          .WithMethods("GET", "POST", "PUT", "DELETE")
                          .WithHeaders("Authorization", "Content-Type", "Tenant-ID")
                          .AllowCredentials();
                });
            });
        }

        private static void ConfigureSwagger(WebApplicationBuilder builder)
        {
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Admin Web API", Version = "v1" });

                // Add Tenant-ID header
                c.AddSecurityDefinition("Tenant", new OpenApiSecurityScheme
                {
                    In = ParameterLocation.Header,
                    Name = "Tenant-ID",
                    Type = SecuritySchemeType.ApiKey,
                    Description = "Tenant ID for multi-tenant authentication"
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Tenant" }
                        },
                        new List<string>()
                    }
                });
            });
        }

        #endregion Private Helper Methods
    }
}