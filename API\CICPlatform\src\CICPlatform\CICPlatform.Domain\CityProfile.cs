﻿namespace CICPlatform.Domain
{
    public class CityProfile : GeneralAttribute
    {
        public int? CityId { get; set; }
        public virtual City City { get; set; }
        public int? EntityTypeId { get; set; }
        public string Address { get; set; }
        public string LegalName { get; set; }
        public string Website { get; set; }
        public int NoOfEmployees { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }
        public string Jurisdiction { get; set; }
        public string Division { get; set; }
        public string GDP { get; set; }
        public string MayorName { get; set; }
        public string MayorContact { get; set; }
        public string MayorEmail { get; set; }
        public string AdminiName { get; set; }
        public string AdminiContact { get; set; }
        public string AdminiEmail { get; set; }
        public int? DocumentStoreId { get; set; }
        public string ImageUrl { get; set; }
        public string Tier { get; set; }
        public long? Population { get; set; }
        public int? ProfileFor { get; set; }
    }
}