﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CICPlatform.Admin.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "CIC");

            migrationBuilder.CreateTable(
                name: "Log",
                schema: "CIC",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TimeStamp = table.Column<string>(type: "text", nullable: true),
                    Level = table.Column<string>(type: "text", nullable: true),
                    Template = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true),
                    Exception = table.Column<string>(type: "text", nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    _ts = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Log", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "Template",
                schema: "CIC",
                columns: table => new
                {
                    TemplateId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "This field is used to give the template a distinctive name."),
                    DashboardJson = table.Column<string>(type: "text", nullable: false),
                    Active = table.Column<short>(type: "smallint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Template", x => x.TemplateId);
                },
                comment: "The application's templates are kept in this table.");

            migrationBuilder.CreateTable(
                name: "AuditLog",
                schema: "CIC",
                columns: table => new
                {
                    AuditLogId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Controller = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Action = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    UserId = table.Column<int>(type: "integer", nullable: true),
                    LocationId = table.Column<int>(type: "integer", nullable: true),
                    IPAddress = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    TimeStamp = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Narration = table.Column<string>(type: "text", nullable: false),
                    Url = table.Column<string>(type: "text", nullable: false),
                    ModuleId = table.Column<int>(type: "integer", nullable: true),
                    Language = table.Column<string>(type: "text", nullable: false),
                    MenuId = table.Column<int>(type: "integer", nullable: true),
                    TransactionId = table.Column<int>(type: "integer", nullable: true),
                    Custom1 = table.Column<int>(type: "integer", nullable: true),
                    Custom2 = table.Column<int>(type: "integer", nullable: true),
                    Custom3 = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Custom4 = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditLog", x => x.AuditLogId);
                });

            migrationBuilder.CreateTable(
                name: "LookUpInfo",
                schema: "CIC",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false),
                    LookUpTypeId = table.Column<int>(type: "integer", nullable: false),
                    BusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    ModuleId = table.Column<int>(type: "integer", nullable: true),
                    SortOrder = table.Column<decimal>(type: "numeric", nullable: true, comment: "The order in which these lookups are displayed in the corresponding screens is specified in this field."),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    SchemaDefinitionId = table.Column<int>(type: "integer", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true),
                    Code = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false, comment: "This field is used to supply the Lookup value with a unique code."),
                    Description = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "The purpose of this field is to give a detailed explanation of the lookup defined for.")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LookUpInfo", x => x.Id);
                },
                comment: "The application's system control lookup values are defined on this table. The majority of the lookups are provided as seed data.");

            migrationBuilder.CreateTable(
                name: "LookUpType",
                schema: "CIC",
                columns: table => new
                {
                    LookUpTypeId = table.Column<int>(type: "integer", nullable: false),
                    ModuleId = table.Column<int>(type: "integer", nullable: false, comment: "Module-wise lookup types are maintained using this field. It will be simple for the user to filter the lookup types by module."),
                    BusinessUnitId = table.Column<int>(type: "integer", nullable: true, comment: "This field maps the business unit to the appropriate lookup types associated with."),
                    FieldName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "This field is used to give the lookup type a distinctive name."),
                    Description = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field serves to give a thorough explanation of the lookup type used for."),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LookUpType", x => x.LookUpTypeId);
                },
                comment: "The application's necessary system control lookup types are defined on this table. The majority of lookup types are provided as seed data.");

            migrationBuilder.CreateTable(
                name: "MenuConfig",
                schema: "CIC",
                columns: table => new
                {
                    MenuId = table.Column<int>(type: "integer", nullable: false),
                    ParentMenuId = table.Column<int>(type: "integer", nullable: true),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "This field is used to provide the menu's title. When the user clicks on a module in the menu tree, this is the name that appears."),
                    ComponentName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true, comment: "This field is used to give the menu's component name"),
                    ModuleId = table.Column<int>(type: "integer", nullable: true, comment: "The module that the menu is made for is specified in this field."),
                    MenuType = table.Column<int>(type: "integer", nullable: false, comment: "This field is used to indicate the type of menu, such as reports, transactions, master, etc."),
                    URL = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "The framework engine can be redirected to external URLs using this field."),
                    SortOrder = table.Column<int>(type: "integer", nullable: true, comment: "The order in which the menus are populated in the menu tree is defined by this field."),
                    Remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "This field is used to give a detailed explanation of the menu that is created for."),
                    IsHidden = table.Column<short>(type: "smallint", nullable: true),
                    TemplateId = table.Column<int>(type: "integer", nullable: true),
                    Icon = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to give the menu a distinctive icon."),
                    Color = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to give the menu a distinctive color."),
                    Path = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "This field is used to dynamically route to the appropriate APIs."),
                    ParamSchema = table.Column<string>(type: "text", nullable: true, comment: ""),
                    BusinessUnitFilterLevel = table.Column<int>(type: "integer", nullable: true),
                    MenuCode = table.Column<string>(type: "character varying(6)", maxLength: 6, nullable: true, comment: "If the same path is used for several menus, this field is utilized to provide a unique code for the menu to appropriately route the framework engine."),
                    ServiceType = table.Column<int>(type: "integer", nullable: true),
                    ServiceSchemaId = table.Column<int>(type: "integer", nullable: true),
                    ServiceParamJson = table.Column<string>(type: "text", nullable: true),
                    Tooltip = table.Column<string>(type: "text", nullable: true),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MenuConfig", x => x.MenuId);
                    table.ForeignKey(
                        name: "FK_MenuConfig_MenuConfig_ParentMenuId",
                        column: x => x.ParentMenuId,
                        principalSchema: "CIC",
                        principalTable: "MenuConfig",
                        principalColumn: "MenuId");
                    table.ForeignKey(
                        name: "FK_MenuConfig_Template_TemplateId",
                        column: x => x.TemplateId,
                        principalSchema: "CIC",
                        principalTable: "Template",
                        principalColumn: "TemplateId");
                },
                comment: "The application's menus are kept in this table.");

            migrationBuilder.CreateTable(
                name: "Module",
                schema: "CIC",
                columns: table => new
                {
                    ModuleId = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, comment: "This field is used for providing a unique code for the module."),
                    Name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "This field is used to give the module a distinctive name."),
                    Description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true, comment: "This field is used to give a detailed explanation of what the module is meant for."),
                    ModuleGroupId = table.Column<int>(type: "integer", nullable: false),
                    SortOrder = table.Column<int>(type: "integer", nullable: true, comment: "The order in which the modules are populated in the menu tree is defined by this field."),
                    Icon = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to give the module a distinctive icon."),
                    Color = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to give the module a distinctive color."),
                    DashboardURL = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "The dashboard's URL is specified in this field in case the user clicks on a module in the menu tree. For instance, the payroll module dashboard report will be displayed immediately if the user clicks on payroll root menu."),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Module", x => x.ModuleId);
                },
                comment: "The purpose of this table is to define a module inside a particular module group that the application offers.");

            migrationBuilder.CreateTable(
                name: "SchemaDefinition",
                schema: "CIC",
                columns: table => new
                {
                    SchemaDefinitionId = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false, comment: "This field is used for providing a unique code for the schema."),
                    SchemaName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false, comment: "This field is used for providing a unique name for the schema."),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false, comment: "This field is used to give a detailed explanation of what the schema is meant for."),
                    SchemaTypeId = table.Column<int>(type: "integer", nullable: true),
                    SchemaStructure = table.Column<string>(type: "text", nullable: false, comment: "This field is used to give the JSON structure of the schema is used for."),
                    BusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    ModuleId = table.Column<int>(type: "integer", nullable: true),
                    IsGlobal = table.Column<short>(type: "smallint", nullable: true),
                    IsPrivate = table.Column<short>(type: "smallint", nullable: true),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true),
                    ParentSchemaDefinitionId = table.Column<int>(type: "integer", nullable: true),
                    DomainSchemaFieldMapping = table.Column<string>(type: "text", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SchemaDefinition", x => x.SchemaDefinitionId);
                    table.ForeignKey(
                        name: "FK_SchemaDefinition_LookUpInfo_SchemaTypeId",
                        column: x => x.SchemaTypeId,
                        principalSchema: "CIC",
                        principalTable: "LookUpInfo",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SchemaDefinition_Module_ModuleId",
                        column: x => x.ModuleId,
                        principalSchema: "CIC",
                        principalTable: "Module",
                        principalColumn: "ModuleId");
                },
                comment: "The application's JSON schemas are kept in this table.");

            migrationBuilder.CreateTable(
                name: "ModuleGroup",
                schema: "CIC",
                columns: table => new
                {
                    ModuleGroupId = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, comment: "This field is used to give the module group a unique code."),
                    Name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "This field is used to give the module group a distinctive name."),
                    Description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true, comment: "The purpose of this field is to give a detailed description of the module group."),
                    SortOrder = table.Column<int>(type: "integer", nullable: true, comment: "This field is used to specify the sequence in which module groups appear in menus and tiles."),
                    Icon = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to give the module group a distinctive icon."),
                    Color = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to give the module group a distinctive color."),
                    DashboardURL = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "This field allows you to specify the dashboard's URL that will be displayed once the user clicks on the module group tile. If a user clicks on a dashboard report for the human resource module group, for example, the report will be landed directly in the layout of the application."),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ModuleGroup", x => x.ModuleGroupId);
                    table.ForeignKey(
                        name: "FK_ModuleGroup_SchemaDefinition_CusSchemaId",
                        column: x => x.CusSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                    table.ForeignKey(
                        name: "FK_ModuleGroup_SchemaDefinition_SysSchemaId",
                        column: x => x.SysSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                },
                comment: "This table is used to define the application's available module groups. A module group consists of several connected submodules.");

            migrationBuilder.CreateTable(
                name: "Permission",
                schema: "CIC",
                columns: table => new
                {
                    PermissionId = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, comment: "A unique code for the permission is entered in this field."),
                    Name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "This field is used to give the permission a distinctive name."),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Permission", x => x.PermissionId);
                    table.ForeignKey(
                        name: "FK_Permission_SchemaDefinition_CusSchemaId",
                        column: x => x.CusSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                    table.ForeignKey(
                        name: "FK_Permission_SchemaDefinition_SysSchemaId",
                        column: x => x.SysSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                },
                comment: "This table is used to specify application-level permissions that can be mapped to roles, such as create, update, and delete.");

            migrationBuilder.CreateTable(
                name: "UserGroup",
                schema: "CIC",
                columns: table => new
                {
                    UserGroupId = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "This field is used to give the user group a unique code."),
                    Name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "This field is used to give the user group a distinctive name."),
                    Email = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to provide the group's common email address for correspondence."),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserGroup", x => x.UserGroupId);
                    table.ForeignKey(
                        name: "FK_UserGroup_SchemaDefinition_CusSchemaId",
                        column: x => x.CusSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                    table.ForeignKey(
                        name: "FK_UserGroup_SchemaDefinition_SysSchemaId",
                        column: x => x.SysSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                },
                comment: "Using this table, groups of specific application users can be created.");

            migrationBuilder.CreateTable(
                name: "UserInfo",
                schema: "CIC",
                columns: table => new
                {
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    UserName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "A user can enter a legitimate user name in this field. Once the user logs into the application, this user name will appear in the account details."),
                    LoginName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true, comment: "This field is used to give the user a unique login name."),
                    DefaultBusinessUnitId = table.Column<int>(type: "integer", nullable: true, comment: "For various reasons, a user may have access to several accounting locations within the application. The business unit that would be the user's primary default location is specified in this field. When a user logs in, this accounting location will be selected as their primary accounting location by default."),
                    DefaultRoleId = table.Column<int>(type: "integer", nullable: true, comment: "A user may have several roles in an application for various objectives. His primary role within his permissible roles is mapped using this field."),
                    AuthenticationSettingsId = table.Column<int>(type: "integer", nullable: true),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, comment: "This option is used to specify the date that the user can start using the application."),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp without time zone", nullable: true, comment: "The end date that the user can access the program is set in this field. If we do not wish to restrict with an end date, this date can be left blank."),
                    Password = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true, comment: "This field is used to set the user's password. This password would be encrypted."),
                    Email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false, comment: "The user's unique email address is defined in this field."),
                    UserImage = table.Column<string>(type: "text", nullable: true),
                    IsLocked = table.Column<int>(type: "integer", nullable: true),
                    Remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    FirstName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    MiddleName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    LastName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    PwdUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    FirstLogin = table.Column<short>(type: "smallint", nullable: true),
                    OldPassword = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserInfo", x => x.UserId);
                    table.ForeignKey(
                        name: "FK_UserInfo_SchemaDefinition_CusSchemaId",
                        column: x => x.CusSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                    table.ForeignKey(
                        name: "FK_UserInfo_SchemaDefinition_SysSchemaId",
                        column: x => x.SysSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                },
                comment: "The purpose of this table is to define the application users.");

            migrationBuilder.CreateTable(
                name: "UserGroupMapping",
                schema: "CIC",
                columns: table => new
                {
                    UserGroupMappingId = table.Column<int>(type: "integer", nullable: false),
                    UserGroupId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false, comment: "Users are mapped to the appropriate user group using this field."),
                    DefaultUserGroup = table.Column<short>(type: "smallint", nullable: true, comment: "This field is used to indicate if the assigned group is the user's default group."),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    OriginBusinessUnitId = table.Column<int>(type: "integer", nullable: true),
                    SysSchemaId = table.Column<int>(type: "integer", nullable: true),
                    SysJSON = table.Column<string>(type: "text", nullable: true),
                    CusSchemaId = table.Column<int>(type: "integer", nullable: true),
                    CusJSON = table.Column<string>(type: "text", nullable: true),
                    ModelDefinitionId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserGroupMapping", x => x.UserGroupMappingId);
                    table.ForeignKey(
                        name: "FK_UserGroupMapping_SchemaDefinition_CusSchemaId",
                        column: x => x.CusSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                    table.ForeignKey(
                        name: "FK_UserGroupMapping_SchemaDefinition_SysSchemaId",
                        column: x => x.SysSchemaId,
                        principalSchema: "CIC",
                        principalTable: "SchemaDefinition",
                        principalColumn: "SchemaDefinitionId");
                    table.ForeignKey(
                        name: "FK_UserGroupMapping_UserGroup_UserGroupId",
                        column: x => x.UserGroupId,
                        principalSchema: "CIC",
                        principalTable: "UserGroup",
                        principalColumn: "UserGroupId",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "The purpose of this table is to map users to user groups.");

            migrationBuilder.CreateTable(
                name: "UserDetail",
                schema: "CIC",
                columns: table => new
                {
                    UserDetailId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    MaximumLoginAttempts = table.Column<int>(type: "integer", nullable: true),
                    LastLoginDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastLogoutDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastLoginIp = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    FavouriteMenu = table.Column<string>(type: "text", nullable: true),
                    FrequentMenu = table.Column<string>(type: "text", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: true),
                    IsSystemDefined = table.Column<short>(type: "smallint", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserDetail", x => x.UserDetailId);
                    table.ForeignKey(
                        name: "FK_UserDetail_UserInfo_UserId",
                        column: x => x.UserId,
                        principalSchema: "CIC",
                        principalTable: "UserInfo",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLog_ModuleId",
                schema: "CIC",
                table: "AuditLog",
                column: "ModuleId");

            migrationBuilder.CreateIndex(
                name: "IX_LookUpInfo_CusSchemaId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_LookUpInfo_LookUpTypeId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "LookUpTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_LookUpInfo_SchemaDefinitionId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "SchemaDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_LookUpInfo_SysSchemaId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_LookUpType_CusSchemaId",
                schema: "CIC",
                table: "LookUpType",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_LookUpType_ModuleId",
                schema: "CIC",
                table: "LookUpType",
                column: "ModuleId");

            migrationBuilder.CreateIndex(
                name: "IX_LookUpType_SysSchemaId",
                schema: "CIC",
                table: "LookUpType",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_MenuConfig_CusSchemaId",
                schema: "CIC",
                table: "MenuConfig",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_MenuConfig_ModuleId",
                schema: "CIC",
                table: "MenuConfig",
                column: "ModuleId");

            migrationBuilder.CreateIndex(
                name: "IX_MenuConfig_ParentMenuId",
                schema: "CIC",
                table: "MenuConfig",
                column: "ParentMenuId");

            migrationBuilder.CreateIndex(
                name: "IX_MenuConfig_SysSchemaId",
                schema: "CIC",
                table: "MenuConfig",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_MenuConfig_TemplateId",
                schema: "CIC",
                table: "MenuConfig",
                column: "TemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_Module_CusSchemaId",
                schema: "CIC",
                table: "Module",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_Module_ModuleGroupId",
                schema: "CIC",
                table: "Module",
                column: "ModuleGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_Module_SysSchemaId",
                schema: "CIC",
                table: "Module",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleGroup_CusSchemaId",
                schema: "CIC",
                table: "ModuleGroup",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleGroup_SysSchemaId",
                schema: "CIC",
                table: "ModuleGroup",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_Permission_CusSchemaId",
                schema: "CIC",
                table: "Permission",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_Permission_SysSchemaId",
                schema: "CIC",
                table: "Permission",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_SchemaDefinition_ModuleId",
                schema: "CIC",
                table: "SchemaDefinition",
                column: "ModuleId");

            migrationBuilder.CreateIndex(
                name: "IX_SchemaDefinition_SchemaTypeId",
                schema: "CIC",
                table: "SchemaDefinition",
                column: "SchemaTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_UserDetail_UserId",
                schema: "CIC",
                table: "UserDetail",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserGroup_CusSchemaId",
                schema: "CIC",
                table: "UserGroup",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroup_SysSchemaId",
                schema: "CIC",
                table: "UserGroup",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupMapping_CusSchemaId",
                schema: "CIC",
                table: "UserGroupMapping",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupMapping_SysSchemaId",
                schema: "CIC",
                table: "UserGroupMapping",
                column: "SysSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_UserGroupMapping_UserGroupId",
                schema: "CIC",
                table: "UserGroupMapping",
                column: "UserGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_UserInfo_CusSchemaId",
                schema: "CIC",
                table: "UserInfo",
                column: "CusSchemaId");

            migrationBuilder.CreateIndex(
                name: "IX_UserInfo_SysSchemaId",
                schema: "CIC",
                table: "UserInfo",
                column: "SysSchemaId");

            migrationBuilder.AddForeignKey(
                name: "FK_AuditLog_Module_ModuleId",
                schema: "CIC",
                table: "AuditLog",
                column: "ModuleId",
                principalSchema: "CIC",
                principalTable: "Module",
                principalColumn: "ModuleId");

            migrationBuilder.AddForeignKey(
                name: "FK_LookUpInfo_LookUpType_LookUpTypeId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "LookUpTypeId",
                principalSchema: "CIC",
                principalTable: "LookUpType",
                principalColumn: "LookUpTypeId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_LookUpInfo_SchemaDefinition_CusSchemaId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "CusSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_LookUpInfo_SchemaDefinition_SchemaDefinitionId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "SchemaDefinitionId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_LookUpInfo_SchemaDefinition_SysSchemaId",
                schema: "CIC",
                table: "LookUpInfo",
                column: "SysSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_LookUpType_Module_ModuleId",
                schema: "CIC",
                table: "LookUpType",
                column: "ModuleId",
                principalSchema: "CIC",
                principalTable: "Module",
                principalColumn: "ModuleId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_LookUpType_SchemaDefinition_CusSchemaId",
                schema: "CIC",
                table: "LookUpType",
                column: "CusSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_LookUpType_SchemaDefinition_SysSchemaId",
                schema: "CIC",
                table: "LookUpType",
                column: "SysSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_MenuConfig_Module_ModuleId",
                schema: "CIC",
                table: "MenuConfig",
                column: "ModuleId",
                principalSchema: "CIC",
                principalTable: "Module",
                principalColumn: "ModuleId");

            migrationBuilder.AddForeignKey(
                name: "FK_MenuConfig_SchemaDefinition_CusSchemaId",
                schema: "CIC",
                table: "MenuConfig",
                column: "CusSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_MenuConfig_SchemaDefinition_SysSchemaId",
                schema: "CIC",
                table: "MenuConfig",
                column: "SysSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_Module_ModuleGroup_ModuleGroupId",
                schema: "CIC",
                table: "Module",
                column: "ModuleGroupId",
                principalSchema: "CIC",
                principalTable: "ModuleGroup",
                principalColumn: "ModuleGroupId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Module_SchemaDefinition_CusSchemaId",
                schema: "CIC",
                table: "Module",
                column: "CusSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");

            migrationBuilder.AddForeignKey(
                name: "FK_Module_SchemaDefinition_SysSchemaId",
                schema: "CIC",
                table: "Module",
                column: "SysSchemaId",
                principalSchema: "CIC",
                principalTable: "SchemaDefinition",
                principalColumn: "SchemaDefinitionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LookUpType_Module_ModuleId",
                schema: "CIC",
                table: "LookUpType");

            migrationBuilder.DropForeignKey(
                name: "FK_SchemaDefinition_Module_ModuleId",
                schema: "CIC",
                table: "SchemaDefinition");

            migrationBuilder.DropForeignKey(
                name: "FK_LookUpInfo_LookUpType_LookUpTypeId",
                schema: "CIC",
                table: "LookUpInfo");

            migrationBuilder.DropForeignKey(
                name: "FK_LookUpInfo_SchemaDefinition_CusSchemaId",
                schema: "CIC",
                table: "LookUpInfo");

            migrationBuilder.DropForeignKey(
                name: "FK_LookUpInfo_SchemaDefinition_SchemaDefinitionId",
                schema: "CIC",
                table: "LookUpInfo");

            migrationBuilder.DropForeignKey(
                name: "FK_LookUpInfo_SchemaDefinition_SysSchemaId",
                schema: "CIC",
                table: "LookUpInfo");

            migrationBuilder.DropTable(
                name: "AuditLog",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "Log",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "MenuConfig",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "Permission",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "UserDetail",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "UserGroupMapping",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "Template",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "UserInfo",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "UserGroup",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "Module",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "ModuleGroup",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "LookUpType",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "SchemaDefinition",
                schema: "CIC");

            migrationBuilder.DropTable(
                name: "LookUpInfo",
                schema: "CIC");
        }
    }
}
