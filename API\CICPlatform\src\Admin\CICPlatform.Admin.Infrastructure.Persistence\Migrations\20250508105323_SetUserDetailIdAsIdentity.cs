﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CICPlatform.Admin.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class SetUserDetailIdAsIdentity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SequenceInfo",
                schema: "CIC");

            migrationBuilder.AlterColumn<int>(
                name: "UserDetailId",
                schema: "CIC",
                table: "UserDetail",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "UserDetailId",
                schema: "CIC",
                table: "UserDetail",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.CreateTable(
                name: "SequenceInfo",
                schema: "CIC",
                columns: table => new
                {
                    SeqId = table.Column<int>(type: "integer", nullable: false),
                    TableName = table.Column<string>(type: "text", nullable: false),
                    IncrementBy = table.Column<int>(type: "integer", nullable: false),
                    MaximumValue = table.Column<int>(type: "integer", nullable: false),
                    MinimumValue = table.Column<int>(type: "integer", nullable: false),
                    NextValue = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SequenceInfo", x => new { x.SeqId, x.TableName });
                });
        }
    }
}
