﻿using AutoMapper;
using CICPlatform.Application.DTOs;
using CICPlatform.Domain.Craft;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Application.Profiles
{
    public class MappingProfile:Profile
    {
        public MappingProfile() 
        {
            CreateMap<PFRevenueIncomeType, RevenueIncomeTypeDto>().ReverseMap();
            CreateMap<PFRevenueIncomeType, CreateRevenueIncomeTypeDto>().ReverseMap();
            CreateMap<PFRevenueIncome, RevenueIncomeDto>().ReverseMap();
            CreateMap<PFRevenueIncome, CreateRevenueIncomeDto>().ReverseMap();
        }
    }
}
