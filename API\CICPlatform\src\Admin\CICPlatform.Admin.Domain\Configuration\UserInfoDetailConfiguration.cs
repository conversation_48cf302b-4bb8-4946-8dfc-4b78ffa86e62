﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration
{
    public class UserInfoDetailConfiguration : IEntityTypeConfiguration<UserInfoDetail>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<UserInfoDetail> builder)
        {
            builder.ToView("UserInfoDetail");
            builder.HasNoKey(); // Mark the entity as keyless
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new UserInfoDetailConfiguration());
        }
    }
}