// Create and configure the builder
using CICPlatform.Admin.API.Configuration;
using CICPlatform.Admin.API.EndpointDefinitions;

var builder = WebApplication.CreateBuilder(args);
builder.ConfigureBuilder();
builder.ConfigureServices();

// Build the application
var app = builder.Build();

// Configure the HTTP request pipeline
app.ConfigureApp();

// Register endpoints
UserEndpointDefinition.RegisterEndpoints(app);

// Start the application
app.Run();