﻿using CICPlatform.Admin.Domain.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public abstract class AuditAttributesConfiguration<TBase>(DbContextFilter contextFilter) : IEntityTypeConfiguration<TBase>
        where TBase : AuditAttributes
    {
        private readonly int businessUnitId = contextFilter.BusinessUnitId;

        void IEntityTypeConfiguration<TBase>.Configure(EntityTypeBuilder<TBase> builder)
        {
            // Do all the configuration specific to `BaseEntity`
            builder.Property(e => e.Active)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Active");
            builder.Property(e => e.OriginBusinessUnitId)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Origin BusinessUnit")
                .HasAnnotation("Namespace", "CICPlatform.Admin.Domain.Shared")
                .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                .HasAnnotation("DisplayMember", "Code-Name");

            builder.Property(e => e.SysSchemaId).IsRequired(false).HasAnnotation("DisplayName", "System SchemaId");
            builder.Property(e => e.CusSchemaId).IsRequired(false).HasAnnotation("DisplayName", "Custom SchemaId");
            builder.Property(e => e.SysJSON).IsRequired(false).HasAnnotation("DisplayName", "System Schema");
            builder.Property(e => e.CusJSON).IsRequired(false).HasAnnotation("DisplayName", "Custom Schema");
            builder.Property(e => e.ModelDefinitionId).IsRequired(false);
            builder.Property(e => e.IsSystemDefined).IsRequired(false);

            builder.HasOne(a => a.SysSchema).WithMany().HasForeignKey(a => a.SysSchemaId).HasPrincipalKey(a => a.SchemaDefinitionId).OnDelete(DeleteBehavior.NoAction);
            builder.HasOne(a => a.CusSchema).WithMany().HasForeignKey(a => a.CusSchemaId).HasPrincipalKey(a => a.SchemaDefinitionId).OnDelete(DeleteBehavior.NoAction);
            builder.Navigation(e => e.SysSchema).AutoInclude();
            builder.Navigation(e => e.CusSchema).AutoInclude();

            //--Global filter
            builder.HasQueryFilter(c => c.Active != -1 /*&& c.OriginBusinessUnitId == this.businessUnitId*/);

            Configure(builder);
        }

        public abstract void Configure(EntityTypeBuilder<TBase> builder);
    }
}