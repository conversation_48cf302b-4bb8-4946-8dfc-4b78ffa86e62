﻿
using CICPlatform.Application.Extensions;
using CICPlatform.Domain;
using CICPlatform.Domain.Craft;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace CICPlatform.Infrastructure.Persistence
{
    public class CICApplicationDbContext: DbContext
    {
        public string DataBase
        {
            get;
            private set;
        }

        public CICApplicationDbContext(DbContextOptions options, IConfiguration configuration) : base(options)
        {
            DataBase = configuration.GetSection("DatabaseSettings:DefaultDB").IsValid();
            switch (DataBase)
            {
                case "PostgreSQL":
                    AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
                    break;

                default:
                    break;
            }
        }

        public CICApplicationDbContext(DbContextOptions options) : base(options)
        {
        }
        public DbSet<PFRevenueIncome> PFRevenueIncome { get; set; }
        public DbSet<PFRevenueIncomeType> PFRevenueIncomeType { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            switch (DataBase)
            {
                case "MySQL":
                    break;

                case "SQLServer":
                default:
                    modelBuilder.HasDefaultSchema("cic_data");
                    break;
            }
            base.OnModelCreating(modelBuilder);
        }
    }
}
