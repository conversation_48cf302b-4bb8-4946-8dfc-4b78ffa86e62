﻿using CICPlatform.Application.Contracts;
using CICPlatform.Application.DTOs;
using CICPlatform.Domain.Craft;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Infrastructure.Persistence.Repositories
{
    public class RevenueIncomeRepository(CICApplicationDbContext applicationDbContext) : GenericRepository<PFRevenueIncome>(applicationDbContext), IRevenueIncomeRepository
    {
        public async Task<List<RevenueIncomeDto>> GetAllRevenueIncome()
        {
            var query = (from income in applicationDbContext.PFRevenueIncome.AsNoTracking()
                         join incometype in applicationDbContext.PFRevenueIncomeType.DefaultIfEmpty()
                         on income.TypeId equals incometype.Id
                         select new RevenueIncomeDto
                         {
                             Id=income.Id,
                             Year = income.Year,
                             Value = income.Value,
                             Code=incometype.Code,
                             Formula=incometype.Formula,
                             Description=incometype.Formula,
                             GuidanceShortTerm=incometype.GuidanceShortTerm,
                             Level=incometype.Level,
                             ParentId=incometype.ParentId,
                             SortOrder=incometype.SortOrder,
                             CityId=incometype.CityId
                         });

            return await query.ToListAsync();
        }
    }
}
