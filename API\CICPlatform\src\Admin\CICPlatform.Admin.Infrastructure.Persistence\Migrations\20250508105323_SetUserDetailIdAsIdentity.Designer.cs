﻿// <auto-generated />
using System;
using CICPlatform.Admin.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CICPlatform.Admin.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(AdminDbContext))]
    [Migration("20250508105323_SetUserDetailIdAsIdentity")]
    partial class SetUserDetailIdAsIdentity
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("CIC")
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.AuditLog", b =>
                {
                    b.Property<int>("AuditLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AuditLogId"));

                    b.Property<string>("Action")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Controller")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("Custom1")
                        .HasColumnType("integer");

                    b.Property<int?>("Custom2")
                        .HasColumnType("integer");

                    b.Property<string>("Custom3")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Custom4")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("LocationId")
                        .HasColumnType("integer");

                    b.Property<int?>("MenuId")
                        .HasColumnType("integer");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<string>("Narration")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("TimeStamp")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("TransactionId")
                        .HasColumnType("integer");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("AuditLogId");

                    b.HasIndex("ModuleId");

                    b.ToTable("AuditLog", "CIC");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.LookUpInfo", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<int?>("BusinessUnitId")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasComment("This field is used to supply the Lookup value with a unique code.")
                        .HasAnnotation("DisplayName", "Code");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("The purpose of this field is to give a detailed explanation of the lookup defined for.")
                        .HasAnnotation("DisplayName", "Description");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int>("LookUpTypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "Shared.Model");

                    b.Property<int?>("SchemaDefinitionId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("SortOrder")
                        .HasColumnType("numeric")
                        .HasComment("The order in which these lookups are displayed in the corresponding screens is specified in this field.")
                        .HasAnnotation("DisplayName", "Sort Order");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("Id");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("LookUpTypeId");

                    b.HasIndex("SchemaDefinitionId");

                    b.HasIndex("SysSchemaId");

                    b.ToTable("LookUpInfo", "CIC", t =>
                        {
                            t.HasComment("The application's system control lookup values are defined on this table. The majority of the lookups are provided as seed data.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.LookUpType", b =>
                {
                    b.Property<int>("LookUpTypeId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<int?>("BusinessUnitId")
                        .HasColumnType("integer")
                        .HasComment("This field maps the business unit to the appropriate lookup types associated with.")
                        .HasAnnotation("DisplayName", "Business Unit");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<string>("Description")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field serves to give a thorough explanation of the lookup type used for.")
                        .HasAnnotation("DisplayName", "Description");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the lookup type a distinctive name.")
                        .HasAnnotation("DisplayName", "Name");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int>("ModuleId")
                        .HasColumnType("integer")
                        .HasComment("Module-wise lookup types are maintained using this field. It will be simple for the user to filter the lookup types by module.")
                        .HasAnnotation("AuditVirtualModel", "Module")
                        .HasAnnotation("DisplayMember", "Name")
                        .HasAnnotation("DisplayName", "Module");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "CICPlatform.Admin.Domain.Shared");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("LookUpTypeId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("ModuleId");

                    b.HasIndex("SysSchemaId");

                    b.ToTable("LookUpType", "CIC", t =>
                        {
                            t.HasComment("The application's necessary system control lookup types are defined on this table. The majority of lookup types are provided as seed data.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.Module", b =>
                {
                    b.Property<int>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasComment("This field is used for providing a unique code for the module.");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the module a distinctive color.");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<string>("DashboardURL")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasComment("The dashboard's URL is specified in this field in case the user clicks on a module in the menu tree. For instance, the payroll module dashboard report will be displayed immediately if the user clicks on payroll root menu.");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasComment("This field is used to give a detailed explanation of what the module is meant for.");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the module a distinctive icon.");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int>("ModuleGroupId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the module a distinctive name.");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "CICPlatform.Admin.Domain.Shared");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasComment("The order in which the modules are populated in the menu tree is defined by this field.");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("ModuleId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("ModuleGroupId");

                    b.HasIndex("SysSchemaId");

                    b.ToTable("Module", "CIC", t =>
                        {
                            t.HasComment("The purpose of this table is to define a module inside a particular module group that the application offers.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.ModuleGroup", b =>
                {
                    b.Property<int>("ModuleGroupId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasComment("This field is used to give the module group a unique code.");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the module group a distinctive color.");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<string>("DashboardURL")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasComment("This field allows you to specify the dashboard's URL that will be displayed once the user clicks on the module group tile. If a user clicks on a dashboard report for the human resource module group, for example, the report will be landed directly in the layout of the application.");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasComment("The purpose of this field is to give a detailed description of the module group.");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the module group a distinctive icon.");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the module group a distinctive name.");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "CICPlatform.Admin.Domain.Shared");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasComment("This field is used to specify the sequence in which module groups appear in menus and tiles.");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("ModuleGroupId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("SysSchemaId");

                    b.ToTable("ModuleGroup", "CIC", t =>
                        {
                            t.HasComment("This table is used to define the application's available module groups. A module group consists of several connected submodules.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.SchemaDefinition", b =>
                {
                    b.Property<int>("SchemaDefinitionId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint");

                    b.Property<int?>("BusinessUnitId")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasComment("This field is used for providing a unique code for the schema.");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasComment("This field is used to give a detailed explanation of what the schema is meant for.");

                    b.Property<string>("DomainSchemaFieldMapping")
                        .HasColumnType("text");

                    b.Property<short?>("IsGlobal")
                        .HasColumnType("smallint");

                    b.Property<short?>("IsPrivate")
                        .HasColumnType("smallint");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit");

                    b.Property<int?>("ParentSchemaDefinitionId")
                        .HasColumnType("integer");

                    b.Property<string>("SchemaName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasComment("This field is used for providing a unique name for the schema.");

                    b.Property<string>("SchemaStructure")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasComment("This field is used to give the JSON structure of the schema is used for.");

                    b.Property<int?>("SchemaTypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("SchemaDefinitionId");

                    b.HasIndex("ModuleId");

                    b.HasIndex("SchemaTypeId");

                    b.ToTable("SchemaDefinition", "CIC", t =>
                        {
                            t.HasComment("The application's JSON schemas are kept in this table.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.Template", b =>
                {
                    b.Property<int>("TemplateId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint");

                    b.Property<string>("DashboardJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("This field is used to give the template a distinctive name.");

                    b.HasKey("TemplateId");

                    b.ToTable("Template", "CIC", t =>
                        {
                            t.HasComment("The application's templates are kept in this table.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.Log", b =>
                {
                    b.Property<int>("id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("id"));

                    b.Property<string>("Exception")
                        .HasColumnType("text");

                    b.Property<string>("Level")
                        .HasColumnType("text");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("Properties")
                        .HasColumnType("text");

                    b.Property<string>("Template")
                        .HasColumnType("text");

                    b.Property<string>("TimeStamp")
                        .HasColumnType("text");

                    b.Property<DateTime?>("_ts")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("id");

                    b.ToTable("Log", "CIC");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.Menu", b =>
                {
                    b.Property<int>("MenuId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<int?>("BusinessUnitFilterLevel")
                        .HasColumnType("integer");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the menu a distinctive color.")
                        .HasAnnotation("DisplayName", "Color");

                    b.Property<string>("ComponentName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("This field is used to give the menu's component name")
                        .HasAnnotation("DisplayName", "Component Name");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the menu a distinctive icon.")
                        .HasAnnotation("DisplayName", "Icon");

                    b.Property<short?>("IsHidden")
                        .HasColumnType("smallint");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<string>("MenuCode")
                        .HasMaxLength(6)
                        .HasColumnType("character varying(6)")
                        .HasComment("If the same path is used for several menus, this field is utilized to provide a unique code for the menu to appropriately route the framework engine.")
                        .HasAnnotation("DisplayName", "Menu Code");

                    b.Property<int>("MenuType")
                        .HasColumnType("integer")
                        .HasComment("This field is used to indicate the type of menu, such as reports, transactions, master, etc.");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("integer")
                        .HasComment("The module that the menu is made for is specified in this field.")
                        .HasAnnotation("AuditVirtualModel", "Module")
                        .HasAnnotation("DisplayMember", "Name")
                        .HasAnnotation("DisplayName", "Module");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "Shared.Model");

                    b.Property<string>("ParamSchema")
                        .HasColumnType("text")
                        .HasComment("")
                        .HasAnnotation("DisplayName", "Parameter Schema");

                    b.Property<int?>("ParentMenuId")
                        .HasColumnType("integer");

                    b.Property<string>("Path")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasComment("This field is used to dynamically route to the appropriate APIs.")
                        .HasAnnotation("DisplayName", "Path");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("This field is used to give a detailed explanation of the menu that is created for.")
                        .HasAnnotation("DisplayName", "Remarks");

                    b.Property<string>("ServiceParamJson")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Service Param Json");

                    b.Property<int?>("ServiceSchemaId")
                        .HasColumnType("integer");

                    b.Property<int?>("ServiceType")
                        .HasColumnType("integer");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("integer")
                        .HasComment("The order in which the menus are populated in the menu tree is defined by this field.")
                        .HasAnnotation("DisplayName", "Sort Order");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("TemplateId")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("This field is used to provide the menu's title. When the user clicks on a module in the menu tree, this is the name that appears.")
                        .HasAnnotation("DisplayName", "Title");

                    b.Property<string>("Tooltip")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Tooltip");

                    b.Property<string>("URL")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasComment("The framework engine can be redirected to external URLs using this field.");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("MenuId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("ModuleId");

                    b.HasIndex("ParentMenuId");

                    b.HasIndex("SysSchemaId");

                    b.HasIndex("TemplateId");

                    b.ToTable("MenuConfig", "CIC", t =>
                        {
                            t.HasComment("The application's menus are kept in this table.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.Permission", b =>
                {
                    b.Property<int>("PermissionId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasComment("A unique code for the permission is entered in this field.")
                        .HasAnnotation("DisplayName", "Code");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the permission a distinctive name.")
                        .HasAnnotation("DisplayName", "Permission");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "Shared.Model");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("PermissionId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("SysSchemaId");

                    b.ToTable("Permission", "CIC", t =>
                        {
                            t.HasComment("This table is used to specify application-level permissions that can be mapped to roles, such as create, update, and delete.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.User", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<int?>("AuthenticationSettingsId")
                        .HasColumnType("integer");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<int?>("DefaultBusinessUnitId")
                        .HasColumnType("integer")
                        .HasComment("For various reasons, a user may have access to several accounting locations within the application. The business unit that would be the user's primary default location is specified in this field. When a user logs in, this accounting location will be selected as their primary accounting location by default.")
                        .HasAnnotation("DisplayName", "Default Business Unit");

                    b.Property<int?>("DefaultRoleId")
                        .HasColumnType("integer")
                        .HasComment("A user may have several roles in an application for various objectives. His primary role within his permissible roles is mapped using this field.")
                        .HasAnnotation("DisplayName", "Default Role");

                    b.Property<DateTime>("EffectiveFrom")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("This option is used to specify the date that the user can start using the application.")
                        .HasAnnotation("DisplayName", "Effective From");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp without time zone")
                        .HasComment("The end date that the user can access the program is set in this field. If we do not wish to restrict with an end date, this date can be left blank.")
                        .HasAnnotation("DisplayName", "Effectie Date");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("The user's unique email address is defined in this field.")
                        .HasAnnotation("DisplayName", "User Email");

                    b.Property<short?>("FirstLogin")
                        .HasColumnType("smallint");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasAnnotation("DisplayName", "First Name");

                    b.Property<int?>("IsLocked")
                        .HasColumnType("integer");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasAnnotation("DisplayName", "Last Name");

                    b.Property<string>("LoginName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("This field is used to give the user a unique login name.")
                        .HasAnnotation("DisplayName", "Login Name");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasAnnotation("DisplayName", "Middle Name");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<string>("OldPassword")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasAnnotation("DisplayName", "Old Password");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "Shared.Model");

                    b.Property<string>("Password")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to set the user's password. This password would be encrypted.")
                        .HasAnnotation("DisplayName", "Password");

                    b.Property<DateTime?>("PwdUpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasAnnotation("DisplayName", "Remarks");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("UserImage")
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasComment("A user can enter a legitimate user name in this field. Once the user logs into the application, this user name will appear in the account details.")
                        .HasAnnotation("DisplayName", "User Name");

                    b.HasKey("UserId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("SysSchemaId");

                    b.ToTable("UserInfo", "CIC", t =>
                        {
                            t.HasComment("The purpose of this table is to define the application users.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserDetail", b =>
                {
                    b.Property<int>("UserDetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("UserDetailId"));

                    b.Property<short?>("Active")
                        .HasColumnType("smallint");

                    b.Property<string>("FavouriteMenu")
                        .HasColumnType("text");

                    b.Property<string>("FrequentMenu")
                        .HasColumnType("text");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("LastLoginIp")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastLogoutDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("MaximumLoginAttempts")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditVirtualModel", "User")
                        .HasAnnotation("DisplayMember", "UserName")
                        .HasAnnotation("DisplayName", "User");

                    b.HasKey("UserDetailId");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("UserDetail", "CIC");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserGroup", b =>
                {
                    b.Property<int>("UserGroupId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the user group a unique code.")
                        .HasAnnotation("DisplayName", "Code");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<string>("Email")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to provide the group's common email address for correspondence.")
                        .HasAnnotation("DisplayName", "Email");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("This field is used to give the user group a distinctive name.")
                        .HasAnnotation("DisplayName", "Name");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "Shared.Model");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.HasKey("UserGroupId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("SysSchemaId");

                    b.ToTable("UserGroup", "CIC", t =>
                        {
                            t.HasComment("Using this table, groups of specific application users can be created.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserGroupMapping", b =>
                {
                    b.Property<int>("UserGroupMappingId")
                        .HasColumnType("integer");

                    b.Property<short?>("Active")
                        .HasColumnType("smallint")
                        .HasAnnotation("DisplayName", "Active");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CusJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "Custom Schema");

                    b.Property<int?>("CusSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "Custom SchemaId");

                    b.Property<short?>("DefaultUserGroup")
                        .HasColumnType("smallint")
                        .HasComment("This field is used to indicate if the assigned group is the user's default group.")
                        .HasAnnotation("DisplayName", "Default User Group");

                    b.Property<short?>("IsSystemDefined")
                        .HasColumnType("smallint");

                    b.Property<int?>("ModelDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int?>("OriginBusinessUnitId")
                        .HasColumnType("integer")
                        .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                        .HasAnnotation("DisplayMember", "Code-Name")
                        .HasAnnotation("DisplayName", "Origin BusinessUnit")
                        .HasAnnotation("Namespace", "Shared.Model");

                    b.Property<string>("SysJSON")
                        .HasColumnType("text")
                        .HasAnnotation("DisplayName", "System Schema");

                    b.Property<int?>("SysSchemaId")
                        .HasColumnType("integer")
                        .HasAnnotation("DisplayName", "System SchemaId");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int>("UserGroupId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer")
                        .HasComment("Users are mapped to the appropriate user group using this field.")
                        .HasAnnotation("DisplayName", "User");

                    b.HasKey("UserGroupMappingId");

                    b.HasIndex("CusSchemaId");

                    b.HasIndex("SysSchemaId");

                    b.HasIndex("UserGroupId");

                    b.ToTable("UserGroupMapping", "CIC", t =>
                        {
                            t.HasComment("The purpose of this table is to map users to user groups.");
                        });
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserInfoDetail", b =>
                {
                    b.Property<bool?>("Active")
                        .HasColumnType("boolean");

                    b.Property<int?>("BusinessUnitId")
                        .HasColumnType("integer");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp without time zone");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("FavouriteMenu")
                        .HasColumnType("text");

                    b.Property<bool?>("FirstLogin")
                        .HasColumnType("boolean");

                    b.Property<string>("FrequentMenu")
                        .HasColumnType("text");

                    b.Property<bool?>("IsLocked")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("LastLoginIp")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastLogoutDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("LoginName")
                        .HasColumnType("text");

                    b.Property<int?>("MaximumLoginAttempts")
                        .HasColumnType("integer");

                    b.Property<string>("Password")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PwdUpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<int?>("UpdatedById")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("UserImage")
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.ToTable((string)null);

                    b.ToView("UserInfoDetail", "CIC");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.AuditLog", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.LookUpInfo", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.LookUpType", "LookUpType")
                        .WithMany("LookUpInfo")
                        .HasForeignKey("LookUpTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", null)
                        .WithMany("LookUps")
                        .HasForeignKey("SchemaDefinitionId");

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CusSchema");

                    b.Navigation("LookUpType");

                    b.Navigation("SysSchema");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.LookUpType", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CusSchema");

                    b.Navigation("Module");

                    b.Navigation("SysSchema");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.Module", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.ModuleGroup", "ModuleGroup")
                        .WithMany("Module")
                        .HasForeignKey("ModuleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CusSchema");

                    b.Navigation("ModuleGroup");

                    b.Navigation("SysSchema");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.ModuleGroup", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CusSchema");

                    b.Navigation("SysSchema");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.SchemaDefinition", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId");

                    b.HasOne("CICPlatform.Admin.Domain.Shared.LookUpInfo", "SchemaType")
                        .WithMany()
                        .HasForeignKey("SchemaTypeId");

                    b.Navigation("Module");

                    b.Navigation("SchemaType");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.Menu", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId");

                    b.HasOne("CICPlatform.Admin.Domain.System.Menu", "ParentMenu")
                        .WithMany()
                        .HasForeignKey("ParentMenuId");

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.Template", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId");

                    b.Navigation("CusSchema");

                    b.Navigation("Module");

                    b.Navigation("ParentMenu");

                    b.Navigation("SysSchema");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.Permission", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CusSchema");

                    b.Navigation("SysSchema");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.User", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CusSchema");

                    b.Navigation("SysSchema");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserDetail", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.System.User", "User")
                        .WithOne()
                        .HasForeignKey("CICPlatform.Admin.Domain.System.UserDetail", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserGroup", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CusSchema");

                    b.Navigation("SysSchema");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserGroupMapping", b =>
                {
                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "CusSchema")
                        .WithMany()
                        .HasForeignKey("CusSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.Shared.SchemaDefinition", "SysSchema")
                        .WithMany()
                        .HasForeignKey("SysSchemaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("CICPlatform.Admin.Domain.System.UserGroup", "UserGroup")
                        .WithMany("UserGroupMapping")
                        .HasForeignKey("UserGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CusSchema");

                    b.Navigation("SysSchema");

                    b.Navigation("UserGroup");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.LookUpType", b =>
                {
                    b.Navigation("LookUpInfo");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.ModuleGroup", b =>
                {
                    b.Navigation("Module");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.Shared.SchemaDefinition", b =>
                {
                    b.Navigation("LookUps");
                });

            modelBuilder.Entity("CICPlatform.Admin.Domain.System.UserGroup", b =>
                {
                    b.Navigation("UserGroupMapping");
                });
#pragma warning restore 612, 618
        }
    }
}
