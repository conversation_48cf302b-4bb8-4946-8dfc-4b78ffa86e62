// Create and configure the builder
using CICPlatform.API.Configuration;
using CICPlatform.API.EndpointDefinitions;

var builder = WebApplication.CreateBuilder(args);
builder.ConfigureBuilder();
builder.ConfigureServices();

// Build the application
var app = builder.Build();

// Configure the HTTP request pipeline
app.ConfigureApp();

// Register endpoints
RevenueIncomeTypeEndpointDefinition.RegisterEndpoints(app);
RevenueIncomeEndpointDefinition.RegisterEndpoints(app);

// Start the application
app.Run();