﻿namespace CICPlatform.Admin.Domain.Shared
{
    public class AuditLog
    {
        public int AuditLogId { get; set; }
        public string Controller { get; set; }
        public string Action { get; set; }
        public int? UserId { get; set; }
        public int? LocationId { get; set; }
        public string IPAddress { get; set; }
        public DateTime? TimeStamp { get; set; }
        public string Narration { get; set; }
        public string Url { get; set; }
        public int? ModuleId { get; set; }
        public virtual Module Module { get; set; }
        public string Language { get; set; }
        public int? MenuId { get; set; }
        public int? TransactionId { get; set; }
        public int? Custom1 { get; set; }
        public int? Custom2 { get; set; }
        public string? Custom3 { get; set; }
        public string? Custom4 { get; set; }
    }
}