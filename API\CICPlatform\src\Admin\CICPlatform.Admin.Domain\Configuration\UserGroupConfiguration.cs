﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration
{
    public sealed class UserGroupConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<UserGroup>(contextFilter), IEntityConfiguration
    {
        public UserGroupConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<UserGroup> builder)
        {
            builder.ToTable("UserGroup");
            builder.ToTable(t => t.HasComment("Using this table, groups of specific application users can be created."));
            builder.HasKey(e => e.UserGroupId);
            builder.Property(e => e.UserGroupId).ValueGeneratedNever();
            builder.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(50)
                .HasAnnotation("DisplayName", "Code")
                .HasComment("This field is used to give the user group a unique code.");
            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .HasAnnotation("DisplayName", "Name")
                .HasComment("This field is used to give the user group a distinctive name.");
            builder.Property(e => e.Email)
                .IsRequired(false)
                .HasMaxLength(50)
                .HasAnnotation("DisplayName", "Email")
                .HasComment("This field is used to provide the group's common email address for correspondence.");

            builder
                .HasMany(e => e.UserGroupMapping)
                .WithOne(e => e.UserGroup)
                .HasForeignKey(e => e.UserGroupId)
                .HasPrincipalKey(e => e.UserGroupId);
            builder.Property(e => e.Active).HasAnnotation("DisplayName", "Active");
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new UserGroupConfiguration(contextFilter, feedSeedData));
        }
    }
}