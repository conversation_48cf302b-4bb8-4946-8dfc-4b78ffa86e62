﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Application.DTOs
{
    public class RevenueIncomeDto
    {
        public int Id { get; set; }
        public string Year { get; set; }
        public int? TypeId { get; set; }
        public decimal Value { get; set; }
        public short Active { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }

        public string Code { get; set; }
        public string Formula { get; set; }
        public string Description { get; set; }
        public string GuidanceShortTerm { get; set; }
        public int Level { get; set; }
        public int? ParentId { get; set; }
        public int SortOrder { get; set; }
        public int? CityId { get; set; }
    }
}
