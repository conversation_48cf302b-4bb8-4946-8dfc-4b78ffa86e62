﻿namespace CICPlatform.Domain.Craft
{
    public class PFRevenueIncomeType : GeneralAttribute
    {
        public string Code { get; set; }
        public string Formula { get; set; }
        public string Description { get; set; }
        public string GuidanceShortTerm { get; set; }
        public int Level { get; set; }
        public int? ParentId { get; set; }
        public virtual PFRevenueIncomeType RevenueIncomeType { get; set; }
        public int SortOrder { get; set; }
        public int? CityId { get; set; }
        public virtual ICollection<PFRevenueIncome> PFRevenueIncomes { get; set; }
    }
}