﻿using Microsoft.AspNetCore.Http;
using CICPlatform.Admin.Domain.Shared;

namespace CICPlatform.Admin.Application.Contracts
{
    public class TenantMiddleware
    {
        private readonly RequestDelegate _next;

        public TenantMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context, TenantEntity tenantContext)
        {          

            string tenantIdHeader = context.Request.Headers["Tenant-ID"];

            if (int.TryParse(tenantIdHeader, out int tenantId))
            {
                tenantContext.TenantId = tenantId;
            }
            await _next(context);
        }
    }
}
