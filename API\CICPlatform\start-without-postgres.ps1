# PowerShell script to start CIC Platform services without PostgreSQL (using external DB)
param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("all", "apis", "gateway")]
    [string]$Services = "all",
    
    [Parameter(Mandatory=$false)]
    [switch]$Build,
    
    [Parameter(Mandatory=$false)]
    [switch]$Detached
)

Write-Host "Starting CIC Platform Services (External Database Mode)..." -ForegroundColor Green

# Set common parameters
$dockerComposeArgs = @()
if ($Build) {
    $dockerComposeArgs += "--build"
}
if ($Detached) {
    $dockerComposeArgs += "-d"
}

# Start services based on selection
switch ($Services) {
    "apis" {
        Write-Host "Starting API services..." -ForegroundColor Yellow
        docker-compose up admin-api cic-api @dockerComposeArgs
    }
    "gateway" {
        Write-Host "Starting API Gateway..." -ForegroundColor Yellow
        docker-compose up api-gateway @dockerComposeArgs
    }
    "all" {
        Write-Host "Starting all services..." -ForegroundColor Yellow
        docker-compose up admin-api cic-api api-gateway @dockerComposeArgs
    }
}

if (-not $Detached) {
    Write-Host "`nServices started. Press Ctrl+C to stop." -ForegroundColor Green
    Write-Host "Access points:" -ForegroundColor Cyan
    Write-Host "  - API Gateway: http://localhost:5004/swagger" -ForegroundColor White
    Write-Host "  - Admin API: http://localhost:5002/swagger" -ForegroundColor White
    Write-Host "  - CIC API: http://localhost:5003/swagger" -ForegroundColor White
    Write-Host "  - Identity Server: http://localhost:5130 (run from Visual Studio)" -ForegroundColor White
    Write-Host ""
    Write-Host "Note: Using external PostgreSQL database at **********:5432" -ForegroundColor Yellow
}
