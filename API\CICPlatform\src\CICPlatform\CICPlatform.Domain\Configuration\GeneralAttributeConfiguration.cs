﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Domain.Configuration
{
    public abstract class GeneralAttributeConfiguration<TBase> : IEntityTypeConfiguration<TBase>
        where TBase : GeneralAttribute
    {
        void IEntityTypeConfiguration<TBase>.Configure(EntityTypeBuilder<TBase> builder)
        {
            // Do all the configuration specific to `BaseEntity`
            builder.Property(e => e.Active)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Active");
            builder.HasKey(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedOnAdd();
            builder.Property(e => e.CreatedById).IsRequired(false);
            builder.Property(e => e.CreatedDate).IsRequired(false);
            builder.Property(e => e.UpdatedById).IsRequired(false);
            builder.Property(e => e.UpdatedDate).IsRequired(false);

            //--Global filter
            builder.HasQueryFilter(c => c.Active != -1);

            Configure(builder);
        }

        public abstract void Configure(EntityTypeBuilder<TBase> builder);
    }
}
