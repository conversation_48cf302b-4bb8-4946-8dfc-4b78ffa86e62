﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CICPlatform.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreateCICApplication : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "cic_data");

            migrationBuilder.CreateTable(
                name: "PFRevenueIncomeType",
                schema: "cic_data",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Formula = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    GuidanceShortTerm = table.Column<string>(type: "text", nullable: false),
                    Level = table.Column<int>(type: "integer", nullable: false),
                    ParentId = table.Column<int>(type: "integer", nullable: true),
                    RevenueIncomeTypeId = table.Column<int>(type: "integer", nullable: false),
                    SortOrder = table.Column<int>(type: "integer", nullable: false),
                    CityId = table.Column<int>(type: "integer", nullable: true),
                    Active = table.Column<short>(type: "smallint", nullable: false),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PFRevenueIncomeType", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PFRevenueIncomeType_PFRevenueIncomeType_RevenueIncomeTypeId",
                        column: x => x.RevenueIncomeTypeId,
                        principalSchema: "cic_data",
                        principalTable: "PFRevenueIncomeType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PFRevenueIncome",
                schema: "cic_data",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Year = table.Column<string>(type: "text", nullable: false),
                    TypeId = table.Column<int>(type: "integer", nullable: true),
                    RevenueIncomeTypeId = table.Column<int>(type: "integer", nullable: false),
                    Value = table.Column<decimal>(type: "numeric", nullable: false),
                    Active = table.Column<short>(type: "smallint", nullable: false),
                    CreatedById = table.Column<int>(type: "integer", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedById = table.Column<int>(type: "integer", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PFRevenueIncome", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PFRevenueIncome_PFRevenueIncomeType_RevenueIncomeTypeId",
                        column: x => x.RevenueIncomeTypeId,
                        principalSchema: "cic_data",
                        principalTable: "PFRevenueIncomeType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PFRevenueIncome_RevenueIncomeTypeId",
                schema: "cic_data",
                table: "PFRevenueIncome",
                column: "RevenueIncomeTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_PFRevenueIncomeType_RevenueIncomeTypeId",
                schema: "cic_data",
                table: "PFRevenueIncomeType",
                column: "RevenueIncomeTypeId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PFRevenueIncome",
                schema: "cic_data");

            migrationBuilder.DropTable(
                name: "PFRevenueIncomeType",
                schema: "cic_data");
        }
    }
}
