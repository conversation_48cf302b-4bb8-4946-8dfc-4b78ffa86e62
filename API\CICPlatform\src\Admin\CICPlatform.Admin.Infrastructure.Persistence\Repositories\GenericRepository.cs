﻿using CICPlatform.Admin.Application.Contracts;
using Microsoft.EntityFrameworkCore;

namespace CICPlatform.Admin.Infrastructure.Persistence.Repositories
{
    public class GenericRepository<T>(AdminDbContext adminDbContext) : IGenericRepository<T> where T : class
    {
        public async Task<T> Add(T entity)
        {
            await adminDbContext.AddAsync(entity);
            return entity;
        }

        public async Task<T> Get(int Id)
        {
            return await adminDbContext.Set<T>().FindAsync(Id);
        }

        public async Task<IReadOnlyList<T>> GetAll()
        {
            return await adminDbContext.Set<T>().ToListAsync();
        }

        public async Task<T> Update(T entity)
        {
            adminDbContext.Set<T>().Update(entity);
            await adminDbContext.SaveChangesAsync();
            return entity;
        }

        public async Task Delete(T entity)
        {
            var entry = adminDbContext.Entry(entity);

            // Mark the entity as inactive instead of deleting
            if (entity.GetType().GetProperty("Active") != null)
            {
                entry.Property("Active").CurrentValue = (short?)-1;
                entry.State = EntityState.Modified;
            }
            else
            {
                adminDbContext.Set<T>().Remove(entity);
            }
        }
    }
}