﻿namespace CICPlatform.Admin.Domain.Shared
{
    public class SchemaDefinition
    {
        public int SchemaDefinitionId { get; set; }
        public string Code { get; set; }
        public string SchemaName { get; set; }
        public string Description { get; set; }
        public int? SchemaTypeId { get; set; }
        public virtual LookUpInfo SchemaType { get; set; }
        public string SchemaStructure { get; set; }
        public int? BusinessUnitId { get; set; }
        public int? ModuleId { get; set; }
        public virtual Module Module { get; set; }
        public short? IsGlobal { get; set; }
        public short? IsPrivate { get; set; }
        public short? IsSystemDefined { get; set; }
        public int? ModelDefinitionId { get; set; }
        public int? ParentSchemaDefinitionId { get; set; }
        public string DomainSchemaFieldMapping { get; set; }

        public short? Active { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? OriginBusinessUnitId { get; set; }
        public ICollection<LookUpInfo> LookUps { get; } = new List<LookUpInfo>();
    }
}