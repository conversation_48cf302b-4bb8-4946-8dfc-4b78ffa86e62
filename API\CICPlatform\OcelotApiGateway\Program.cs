﻿using MMLib.SwaggerForOcelot.DependencyInjection;
using OcelotApiGateway.Extensions;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog();

// Configure app configuration
builder.Configuration.AddOcelotWithSwaggerSupport(options =>
{
    options.Folder = "OcelotConfiguration";
});

// Add environment-specific configuration
var env = builder.Environment.EnvironmentName;
builder.Configuration.AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true);

// Configure all services using extension method
builder.ConfigureServices();

var app = builder.Build();

// Configure the HTTP request pipeline using extension method
app.ConfigureMiddleware();

// Start the application
app.Run();