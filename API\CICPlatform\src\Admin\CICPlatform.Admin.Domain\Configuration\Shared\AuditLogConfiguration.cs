﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public sealed class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<AuditLog> builder)
        {
            builder.ToTable("AuditLog");
            builder.HasKey(e => e.AuditLogId);
            builder.Property(e => e.Controller).IsRequired(false).HasMaxLength(100);
            builder.Property(e => e.Action).IsRequired(false).HasMaxLength(20);
            builder.Property(e => e.IPAddress).IsRequired(false).HasMaxLength(20);

            builder.Property(e => e.Custom1).IsRequired(false);
            builder.Property(e => e.Custom2).IsRequired(false);
            builder.Property(e => e.Custom3).IsRequired(false).HasMaxLength(2000);
            builder.Property(e => e.Custom4).IsRequired(false).HasMaxLength(2000);
            builder.HasOne(e => e.Module).WithMany().HasForeignKey(a => a.ModuleId);
            builder.Navigation(e => e.Module).AutoInclude();
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilterbool, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new AuditLogConfiguration());
        }
    }
}