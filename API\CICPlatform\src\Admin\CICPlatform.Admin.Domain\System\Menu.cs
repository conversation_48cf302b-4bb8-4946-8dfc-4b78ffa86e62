﻿using CICPlatform.Admin.Domain.Shared;

namespace CICPlatform.Admin.Domain.System
{
    public class Menu : AuditAttributes
    {
        public int MenuId { get; set; }
        public int? ParentMenuId { get; set; }
        public virtual Menu ParentMenu { get; set; }
        public string Title { get; set; }
        public string? ComponentName { get; set; }
        public int? ModuleId { get; set; }
        public virtual Module Module { get; set; }
        public int MenuType { get; set; }
        public string? URL { get; set; }
        public int? SortOrder { get; set; }
        public string? Remarks { get; set; }
        public short? IsHidden { get; set; }
        public int? TemplateId { get; set; }
        public virtual Template Template { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string? Path { get; set; }
        public string? ParamSchema { get; set; }
        public int? BusinessUnitFilterLevel { get; set; }
        public string? MenuCode { get; set; }
        public int? ServiceType { get; set; }
        public int? ServiceSchemaId { get; set; }
        public string? ServiceParamJson { get; set; }
        public string? Tooltip { get; set; }
    }
}