{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "AllowedOrigins": [
    "http://localhost:5004",
    "http://localhost:5002",
    "http://localhost:5003"
  ],
  //"ConnectionStrings": {
  //  "IdentityServer": "Server=**********;Port=5432;Database=identityserver;Username=techskill;Password=**************"
  //},
  "UseDatabase": false,
  "Certificate": {
    "Path": "certs/identityserver.pfx",
    "Password": "YourSecurePassword"
  },
  "IdentityServer": {
    "BaseUrl": "http://localhost:5130",
    "ClientId": "ClientId",
    "ClientSecret": "ClientSecret",
    "Scope": [ "OcelotGateway", "admin.read", "cic.read" ]
  }
}
