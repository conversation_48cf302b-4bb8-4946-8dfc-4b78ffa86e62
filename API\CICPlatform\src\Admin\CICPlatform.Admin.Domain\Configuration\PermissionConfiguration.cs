﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration
{
    public sealed class PermissionConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<Permission>(contextFilter), IEntityConfiguration
    {
        public PermissionConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<Permission> builder)
        {
            builder.ToTable("Permission");
            builder.ToTable(t => t.HasComment("This table is used to specify application-level permissions that can be mapped to roles, such as create, update, and delete."));
            builder.HasKey(e => e.PermissionId);
            builder.Property(e => e.PermissionId).ValueGeneratedNever();
            builder.Property(e => e.Code)
                .IsRequired()
                .HasAnnotation("DisplayName", "Code")
                .HasMaxLength(10).HasComment("A unique code for the permission is entered in this field.");
            builder.Property(e => e.Name)
                .IsRequired()
                .HasAnnotation("DisplayName", "Permission")
                .HasMaxLength(50).HasComment("This field is used to give the permission a distinctive name.");
            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<Permission>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new PermissionConfiguration(contextFilter, feedSeedData));
        }
    }
}