{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.Hosting.Lifetime": "Information", "Ocelot": "Debug", "System.Net.Http.HttpClient": "Information"}}, "AllowedHosts": "*", "AllowedOrigins": ["http://localhost:5002", "http://localhost:5003", "http://localhost:5130", "http://localhost:4200", "https://localhost:7001", "https://localhost:7002", "https://localhost:7003"], "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "pannapps.co", "TenantId": "93708259-3510-4cf8-bfab-b0802727f5e4", "ClientId": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "Audience": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "AudienceURI": "api://c0356a99-ac1b-4857-92db-1fcfc1c47d81", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "JwtSettings": {"ValidateIssuer": true, "ValidateAudience": false, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ValidIssuer": "https://login.microsoftonline.com/93708259-3510-4cf8-bfab-b0802727f5e4/v2.0", "ClockSkew": "00:05:00"}, "IdentityServer": {"Authority": "http://localhost:5130"}, "Services": {"admin": {"DownstreamPath": "http://admin-api:5002", "HealthCheckPath": "/health"}, "cic": {"DownstreamPath": "http://cic-api:5003", "HealthCheckPath": "/health"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information", "System": "Warning", "Ocelot": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/ocelot-dev-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/ocelot-errors-.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Error", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithEnvironmentName"]}, "RateLimiting": {"ClientWhitelist": ["trusted-client", "development-client"], "EnableRateLimiting": false, "Period": "1m", "PeriodTimespan": 60, "Limit": 100}, "Resilience": {"ExceptionsAllowedBeforeBreaking": 5, "DurationOfBreak": 5000, "TimeoutValue": 10000}, "HealthChecks": {"Enabled": true, "Interval": "00:01:00", "Timeout": "00:00:30"}, "Security": {"RequireHttps": false, "EnableCors": true, "AllowCredentials": true, "EnableDetailedErrors": true}, "Performance": {"EnableResponseCaching": false, "EnableCompression": true, "MaxRequestBodySize": 52428800}, "Development": {"EnableSwagger": true, "EnableDeveloperExceptionPage": true, "EnableSensitiveDataLogging": true, "BypassAuthentication": false, "MockExternalServices": false}}