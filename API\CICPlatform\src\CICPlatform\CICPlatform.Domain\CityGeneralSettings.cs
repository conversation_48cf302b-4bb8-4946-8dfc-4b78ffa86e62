﻿namespace CICPlatform.Domain
{
    public class CityGeneralSettings : GeneralAttribute
    {
        public int? CityId { get; set; }
        public virtual City City { get; set; }
        public int CurrentFinancialYearId { get; set; }
        public int? CountOfPastYear { get; set; }
        public int? CountOfFutureYear { get; set; }
        public int FinancialYearEndingId { get; set; }
        public int FinancialsIn { get; set; }
        public decimal? PopulationGrowthRate { get; set; }
        public string ProjectionBasedOn { get; set; }
        public int? CountOfSelectedYears { get; set; }
    }
}