﻿using Duende.IdentityServer.Models;

namespace IdentityServer
{
    public static class Config
    {
        // Define API scopes here
        public static IEnumerable<ApiScope> ApiScopes =>
            new List<ApiScope>
            {
                new ApiScope("OcelotGateway", "Ocelot API Access"),
                new ApiScope("admin.read", "Read access to admin API"),
                new ApiScope("admin.write", "Write access to admin API"),
                new ApiScope("cic.read", "Read access to CIC API"),
                new ApiScope("cic.write", "Write access to CIC API")
            };

        // Link API resources to the scope
        public static IEnumerable<ApiResource> Apis =>
            new List<ApiResource>
            {
                new ApiResource("OcelotGateway", "Ocelot Gateway")
                {
                    Scopes = { "OcelotGateway" },
                    // Add claim types that should be included in the access token
                    UserClaims = { "role" }
                },
                new ApiResource("admin-api", "Admin API")
                {
                    Scopes = { "admin.read", "admin.write" },
                    // Only include the role claim type
                    UserClaims = { "role" }
                },
                new ApiResource("cic-api", "CIC API")
                {
                    Scopes = { "cic.read", "cic.write" },
                    // Only include the role claim type
                    UserClaims = { "role" }
                }
            };

        // Client is allowed to access the scope
        public static IEnumerable<Client> Clients =>
            new List<Client>
            {
                // Machine-to-machine client (client credentials)
                new Client
                {
                    ClientId = "ClientId",
                    ClientName = "API Gateway Client",
                    // Allow both client credentials and resource owner password
                    AllowedGrantTypes = new[] { GrantType.ClientCredentials, GrantType.ResourceOwnerPassword },
                    ClientSecrets =
                    {
                        new Secret("ClientSecret".Sha256())
                    },
                    AllowedScopes = { "OcelotGateway", "admin.read", "admin.write", "cic.read", "offline_access" },
                    AccessTokenLifetime = 3600, // 1 hour
                    // More restrictive settings for security
                    AllowOfflineAccess = true, // Enable offline access for refresh tokens
                    RequireClientSecret = true,
                    RequireConsent = false,
                    AlwaysSendClientClaims = true,
                    AlwaysIncludeUserClaimsInIdToken = true,
                    //// Add client claims directly
                    //Claims =
                    //{
                    //    new ClientClaim("role", "Admin")
                    //},
                    // Enable refresh tokens
                    RefreshTokenUsage = TokenUsage.OneTimeOnly,
                    RefreshTokenExpiration = TokenExpiration.Absolute,
                    AbsoluteRefreshTokenLifetime = 86400 // 24 hours
                },

                // Interactive client for user authentication
                new Client
                {
                    ClientId = "web-client",
                    ClientName = "Web Client Application",
                    AllowedGrantTypes = GrantTypes.Code,
                    RequirePkce = true, // Recommended for security
                    RequireClientSecret = false, // Public client

                    RedirectUris = { "https://localhost:5004/signin-oidc" },
                    PostLogoutRedirectUris = { "https://localhost:5004/signout-callback-oidc" },

                    AllowedScopes = {
                        "openid",
                        "profile",
                        "OcelotGateway",
                        "admin.read",
                        "admin.write",
                        "cic.read",
                        "cic.write"
                    },

                    AllowOfflineAccess = true,
                    AccessTokenLifetime = 3600, // 1 hour
                    IdentityTokenLifetime = 300, // 5 minutes

                    // More restrictive settings for security
                    RequireConsent = true,
                    AlwaysSendClientClaims = true,
                    AlwaysIncludeUserClaimsInIdToken = true
                }
            };
    }
}