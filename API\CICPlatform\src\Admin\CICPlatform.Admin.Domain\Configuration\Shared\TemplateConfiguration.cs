﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public sealed class TemplateConfiguration(bool feedSeedData = false) : IEntityTypeConfiguration<Template>, IEntityConfiguration
    {
        public TemplateConfiguration() : this(false)
        {
        }

        public void Configure(EntityTypeBuilder<Template> builder)
        {
            builder.ToTable("Template");
            builder.ToTable(t => t.HasComment("The application's templates are kept in this table."));
            builder.<PERSON><PERSON>ey(e => e.TemplateId);
            builder.Property(e => e.TemplateId).ValueGeneratedNever();
            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .HasComment("This field is used to give the template a distinctive name.");
            builder.HasQueryFilter(c => c.Active != -1);

            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<Template>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new TemplateConfiguration(feedSeedData));
        }
    }
}