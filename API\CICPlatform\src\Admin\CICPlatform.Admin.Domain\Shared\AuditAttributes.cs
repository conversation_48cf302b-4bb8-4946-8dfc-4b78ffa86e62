﻿namespace CICPlatform.Admin.Domain.Shared
{

    public class AuditAttributes : TenantEntity
    {
        public short? IsSystemDefined { get; set; }
        public short? Active { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public int? OriginBusinessUnitId { get; set; }
        public int? SysSchemaId { get; set; }
        public virtual SchemaDefinition SysSchema { get; set; }
        public string? SysJSON { get; set; }
        public int? CusSchemaId { get; set; }
        public virtual SchemaDefinition CusSchema { get; set; }
        public string? CusJSON { get; set; }
        public int? ModelDefinitionId { get; set; }
    }
}