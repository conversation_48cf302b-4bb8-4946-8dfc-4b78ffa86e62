﻿
using CICPlatform.Domain.Contracts;
using CICPlatform.Domain.Craft;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Domain.Configuration
{
    public sealed class PFRevenueIncomeConfiguration(bool feedSeedData = false) : GeneralAttributeConfiguration<PFRevenueIncome>, IEntityConfiguration
    {
        public override void Configure(EntityTypeBuilder<PFRevenueIncome> builder)
        {
            builder.ToTable("RevenueIncome");
            builder.Property(u => u.Year).IsRequired().HasMaxLength(10);
            builder.Property(u => u.Value).IsRequired().HasPrecision(18, 2); 

            builder.HasOne(u => u.RevenueIncomeType)
               .WithMany(r => r.PFRevenueIncomes)
               .HasForeignKey(u => u.TypeId)
               .OnDelete(DeleteBehavior.Restrict);

            builder.HasQueryFilter(c => c.Active != -1);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new PFRevenueIncomeConfiguration());
        }

    }
}
