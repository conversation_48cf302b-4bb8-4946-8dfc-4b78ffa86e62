<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Duende.IdentityModel" Version="7.0.0" />
    <PackageReference Include="Duende.IdentityServer" Version="7.2.3" />
	<PackageReference Include="Duende.IdentityServer.EntityFramework" Version="7.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
	<PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
  </ItemGroup>

</Project>
