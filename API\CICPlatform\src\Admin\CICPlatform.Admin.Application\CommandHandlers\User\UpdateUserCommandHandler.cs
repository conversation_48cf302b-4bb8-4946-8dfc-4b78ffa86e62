﻿using AutoMapper;
using CICPlatform.Admin.Application.Commands;
using CICPlatform.Admin.Application.Contracts;
using CICPlatform.Admin.Application.DTOs.User;
using MediatR;

namespace CICPlatform.Admin.Application.CommandHandlers.User
{
    public class UpdateUserCommandHandler(IUnitOfWork unitOfWork, IMapper mapper, IMediator mediator) : IRequestHandler<UpdateUserCommand, UserDTO>
    {
        public async Task<UserDTO> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
        {
            var user = await unitOfWork.UserRepository.Get(request.UpdateUserDto.UserId);
            mapper.Map(request.UpdateUserDto, user);
            var userLists = await unitOfWork.UserRepository.Update(user);
            await unitOfWork.Save();
            return mapper.Map<UserDTO>(userLists);
        }
    }
}