﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public sealed class LookUpInfoConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : MasterAttributesConfiguration<LookUpInfo>(contextFilter), IEntityConfiguration
    {
        public LookUpInfoConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<LookUpInfo> builder)
        {
            builder.ToTable("LookUpInfo");
            builder.ToTable(t => t.HasComment("The application's system control lookup values are defined on this table. The majority of the lookups are provided as seed data."));
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.Code)
                .HasAnnotation("DisplayName", "Code")
                .IsRequired()
                .HasMaxLength(5)
                .HasComment("This field is used to supply the Lookup value with a unique code.");
            builder.Property(e => e.Description)
                .HasAnnotation("DisplayName", "Description")
                .IsRequired()
                .HasMaxLength(50)
                .HasComment("The purpose of this field is to give a detailed explanation of the lookup defined for.");
            builder.Property(e => e.SortOrder)
                .HasAnnotation("DisplayName", "Sort Order")
                .IsRequired(false)
                .HasComment("The order in which these lookups are displayed in the corresponding screens is specified in this field.");
            builder.Property(e => e.Active).HasAnnotation("DisplayName", "Active");

            if (feedSeedData)
            {
                builder.HasData(EntityConfigurationExtension.GetDataFromJson<LookUpInfo>());
            }
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new LookUpInfoConfiguration(contextFilter, feedSeedData));
        }
    }
}