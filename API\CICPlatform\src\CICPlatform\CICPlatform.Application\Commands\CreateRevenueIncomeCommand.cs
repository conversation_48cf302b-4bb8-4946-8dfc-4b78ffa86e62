﻿using CICPlatform.Application.DTOs;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Application.Commands
{
    public class CreateRevenueIncomeCommand : IRequest<RevenueIncomeDto>
    {
        public CreateRevenueIncomeDto CreateRevenueIncomeDto { get; set; }

        public CreateRevenueIncomeCommand()
        {
            CreateRevenueIncomeDto = new CreateRevenueIncomeDto();
        }
    }
}
