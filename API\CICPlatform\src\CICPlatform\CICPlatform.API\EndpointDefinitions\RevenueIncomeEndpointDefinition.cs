﻿using CICPlatform.Application.Commands;
using CICPlatform.Application.DTOs;
using CICPlatform.Application.Models;
using CICPlatform.Application.Queries;
using MediatR;
using System.Net;

namespace CICPlatform.API.EndpointDefinitions
{
    public static class RevenueIncomeEndpointDefinition
    {
        public static void RegisterEndpoints(WebApplication app)
        {
            var RevenueIncomeRouteGroup = app.MapGroup("/api/v1/revenueincomes")
                .WithTags("revenueincomes");
            RevenueIncomeRouteGroup.MapPost("/", CreateRevenueIncome)
                .WithName("CreateRevenueIncome")
                .Produces<APIResponse>(200).Produces(400);
            RevenueIncomeRouteGroup.MapGet("/", GetAllRevenueIncome)
               .WithName("GetRevenueIncome")
               .Produces<APIResponse>(200, "application/json");
        }

        /// <summary>
        /// Create RevenueIncome Type
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="id"></param>
        /// <returns></returns>

        public static async Task<IResult> CreateRevenueIncome(IMediator mediator, CreateRevenueIncomeDto createRevenueIncomeDto)
        {
            var command = new CreateRevenueIncomeCommand { CreateRevenueIncomeDto = createRevenueIncomeDto };
            APIResponse resultObj = new();
            var user = await mediator.Send(command);
            resultObj.IsSuccess = true;
            resultObj.Result = user;
            resultObj.StatusCode = HttpStatusCode.OK;

            return TypedResults.Ok(resultObj);
        }

        /// <summary>
        /// Create RevenueIncome Type
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        /// 

        public static async Task<IResult>GetAllRevenueIncome(IMediator mediator)
        {
            APIResponse resultObj = new();
            var userList = await mediator.Send(new GetAllRevenueIncomeRequest());

            resultObj.IsSuccess = true;
            resultObj.Result = userList;
            resultObj.StatusCode = HttpStatusCode.OK;
            return TypedResults.Ok(resultObj);
        }

    }
}
