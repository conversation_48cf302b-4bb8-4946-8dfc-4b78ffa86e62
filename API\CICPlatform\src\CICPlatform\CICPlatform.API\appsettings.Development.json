{"ConnectionStrings": {"Default": "Server=postgres;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;", "Redis": "redis:6379"}, "DatabaseSettings": {"DefaultDB": "PostgreSQL", "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "CommandTimeout": 30, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "pannapps.co", "TenantId": "93708259-3510-4cf8-bfab-b0802727f5e4", "ClientId": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "Audience": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "AudienceURI": "api://c0356a99-ac1b-4857-92db-1fcfc1c47d81", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "JwtSettings": {"ValidateIssuer": true, "ValidateAudience": false, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ValidIssuer": "https://login.microsoftonline.com/93708259-3510-4cf8-bfab-b0802727f5e4/v2.0", "ClockSkew": "00:05:00"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.EntityFrameworkCore": "Information", "CICPlatform": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information", "System": "Warning", "CICPlatform": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/cic-api-dev-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/cic-api-errors-.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Error", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithEnvironmentName"]}, "AllowedHosts": "*", "AllowedOrigins": ["http://localhost:5004", "http://localhost:4200", "https://localhost:7001"], "Caching": {"DefaultCacheExpiration": "00:15:00", "EnableDistributedCache": true, "CacheProvider": "Redis"}, "Security": {"RequireHttps": false, "EnableCors": true, "AllowCredentials": true, "EnableDetailedErrors": true, "EnableSensitiveDataLogging": true}, "Performance": {"EnableResponseCaching": false, "EnableCompression": true, "MaxRequestBodySize": 52428800}, "HealthChecks": {"Enabled": true, "DatabaseCheck": true, "ExternalServiceCheck": false}, "Development": {"EnableSwagger": true, "EnableDeveloperExceptionPage": true, "EnableSensitiveDataLogging": true, "BypassAuthentication": false, "MockExternalServices": false, "SeedTestData": true}, "Authorization": {"EnableRoleBasedAuthorization": true, "DefaultRole": "User", "CICRole": "CIC", "RequireEmailVerification": false}, "ExternalServices": {"Timeout": "00:00:30", "RetryCount": 3, "RetryDelay": "00:00:02"}}