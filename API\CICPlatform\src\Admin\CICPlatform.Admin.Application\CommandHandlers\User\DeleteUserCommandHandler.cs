﻿using CICPlatform.Admin.Application.Commands;
using CICPlatform.Admin.Application.Contracts;
using CICPlatform.Admin.Application.Exceptions;
using FluentValidation.Results;
using MediatR;

namespace CICPlatform.Admin.Application.CommandHandlers.User
{
    public class DeleteUserCommandHandler(IUnitOfWork unitOfWork) : IRequestHandler<DeleteUserCommand, Unit>
    {
        public async Task<Unit> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
        {
            // Retrieve the user to be deleted
            var user = await unitOfWork.UserRepository.Get(request.Id);

            // Check if the user exists
            if (user == null)
            {
                ValidationResult error = new ValidationResult();
                error.Errors.Add(new ValidationFailure("", "Record Not Exists!!"));
                throw new ValidationException(error);
            }

            // Proceed to delete the user
            await unitOfWork.UserRepository.Delete(user);
            await unitOfWork.Save();
            return Unit.Value;
        }
    }
}