version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: cic-postgres
    environment:
      POSTGRES_DB: techskill
      POSTGRES_USER: techskill
      POSTGRES_PASSWORD: **************
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - cic-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U techskill -d techskill"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cic-redis
    ports:
      - "6379:6379"
    networks:
      - cic-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Identity Server
  identity-server:
    build:
      context: ./Authentication
      dockerfile: Dockerfile
    container_name: cic-identity-server
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5130
    ports:
      - "5130:5130"
    networks:
      - cic-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5130/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin API
  admin-api:
    build:
      context: .
      dockerfile: src/Admin/CICPlatform.Admin.API/Dockerfile
    container_name: cic-admin-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5002
      - ConnectionStrings__Default=Server=postgres;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5002:5002"
    networks:
      - cic-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CIC API
  cic-api:
    build:
      context: .
      dockerfile: src/CICPlatform/CICPlatform.API/Dockerfile
    container_name: cic-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5003
      - ConnectionStrings__Default=Server=postgres;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5003:5003"
    networks:
      - cic-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ocelot API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: OcelotApiGateway/Dockerfile
    container_name: cic-api-gateway
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5004
      - Services__admin__DownstreamPath=http://admin-api:5002
      - Services__cic__DownstreamPath=http://cic-api:5003
      - IdentityServer__Authority=http://identity-server:5130
    ports:
      - "5004:5004"
    networks:
      - cic-network
    depends_on:
      admin-api:
        condition: service_healthy
      cic-api:
        condition: service_healthy
      identity-server:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  cic-network:
    driver: bridge

volumes:
  postgres_data:
