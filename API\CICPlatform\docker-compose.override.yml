version: '3.8'

# Override file for development - allows running services independently
services:
  # Override for local development with localhost URLs
  api-gateway:
    environment:
      - Services__admin__DownstreamPath=http://localhost:5002
      - Services__cic__DownstreamPath=http://localhost:5003
      - IdentityServer__Authority=http://localhost:5130
    depends_on: []

  admin-api:
    environment:
      - ConnectionStrings__Default=Server=localhost;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
      - ConnectionStrings__Redis=localhost:6379
    depends_on: []

  cic-api:
    environment:
      - ConnectionStrings__Default=Server=localhost;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;
      - ConnectionStrings__Redis=localhost:6379
    depends_on: []
