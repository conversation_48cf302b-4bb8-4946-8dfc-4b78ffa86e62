﻿using CICPlatform.Admin.Application.Contracts;

namespace CICPlatform.Admin.Infrastructure.Persistence.Repositories
{
    public class UnitOfWork(AdminDbContext adminDbContext) : IUnitOfWork
    {
        private IUserRepository _userRepository;
        private IUserDetailRepository _userDetailRepository;

        public IUserRepository UserRepository => _userRepository ?? new UserRepository(adminDbContext);
        public IUserDetailRepository UserDetailRepository => _userDetailRepository ?? new UserDetailRepository(adminDbContext);

        public void Dispose()
        {
            adminDbContext.Dispose();
            GC.SuppressFinalize(this);
        }

        public async Task Save()
        {
            await adminDbContext.SaveChangesAsync();
        }
    }
}