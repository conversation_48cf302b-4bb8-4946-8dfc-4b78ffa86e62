﻿using System.Text.Json;

namespace CICPlatform.Admin.Domain.Configuration
{
    public static class EntityConfigurationExtension
    {
        public static List<TEntity> GetDataFromJson<TEntity>()
        {
            string currentDirectory = Directory.GetCurrentDirectory();

            string path = "SeedData";
            string fileName = String.Empty;
            var result = new List<TEntity>();

            string modelName = typeof(TEntity).Name;
            fileName = modelName.ToLower() + ".json";

            string fullPath = Path.Combine(currentDirectory, path, fileName);
            try
            {
                if (!File.Exists(fullPath))
                {
                    throw new ArgumentException($"The file {fullPath} does not exist");
                }

                using (StreamReader reader = new StreamReader(fullPath))
                {
                    string json = reader.ReadToEnd();
                    if (json != null && json != string.Empty)
                    {
                        result = JsonSerializer.Deserialize<List<TEntity>>(json);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}