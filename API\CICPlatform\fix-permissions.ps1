# PowerShell script to fix Docker permission issues
Write-Host "Fixing Docker permission issues..." -ForegroundColor Green

# Stop all containers
Write-Host "Stopping all containers..." -ForegroundColor Yellow
docker-compose down

# Remove any existing containers and images to force rebuild
Write-Host "Removing existing containers and images..." -ForegroundColor Yellow
docker-compose down --rmi all --volumes --remove-orphans

# Clean up Docker system
Write-Host "Cleaning up Docker system..." -ForegroundColor Yellow
docker system prune -f

# Rebuild and start services
Write-Host "Rebuilding and starting services..." -ForegroundColor Yellow

# Start infrastructure first
Write-Host "Starting infrastructure..." -ForegroundColor Cyan
docker-compose up --build postgres redis -d

# Wait for infrastructure to be ready
Write-Host "Waiting for infrastructure to be ready..." -ForegroundColor Cyan
Start-Sleep 15

# Start APIs
Write-Host "Starting APIs..." -ForegroundColor Cyan
docker-compose up --build admin-api cic-api -d

# Wait for APIs to be ready
Write-Host "Waiting for APIs to be ready..." -ForegroundColor Cyan
Start-Sleep 20

# Start gateway
Write-Host "Starting API Gateway..." -ForegroundColor Cyan
docker-compose up --build api-gateway -d

# Wait for gateway to start
Write-Host "Waiting for gateway to start..." -ForegroundColor Cyan
Start-Sleep 15

# Check health
Write-Host "Checking service health..." -ForegroundColor Green
.\check-health.ps1

Write-Host ""
Write-Host "Fix complete! If you still see permission errors:" -ForegroundColor Green
Write-Host "1. Make sure Identity Server is running from Visual Studio on port 5130" -ForegroundColor Yellow
Write-Host "2. Check logs: docker-compose logs api-gateway" -ForegroundColor Yellow
Write-Host "3. Try running individual services: docker-compose up admin-api -d" -ForegroundColor Yellow
