# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/CICPlatform/CICPlatform.API/CICPlatform.API.csproj", "src/CICPlatform/CICPlatform.API/"]
COPY ["src/CICPlatform/CICPlatform.Application/CICPlatform.Application.csproj", "src/CICPlatform/CICPlatform.Application/"]
COPY ["src/CICPlatform/CICPlatform.Domain/CICPlatform.Domain.csproj", "src/CICPlatform/CICPlatform.Domain/"]
COPY ["src/CICPlatform/CICPlatform.Infrastructure/CICPlatform.Infrastructure.csproj", "src/CICPlatform/CICPlatform.Infrastructure/"]
COPY ["src/CICPlatform/CICPlatform.Infrastructure.Persistence/CICPlatform.Infrastructure.Persistence.csproj", "src/CICPlatform/CICPlatform.Infrastructure.Persistence/"]
RUN dotnet restore "./src/CICPlatform/CICPlatform.API/CICPlatform.API.csproj"
COPY . .
WORKDIR "/src/src/CICPlatform/CICPlatform.API"
RUN dotnet build "./CICPlatform.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./CICPlatform.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CICPlatform.API.dll"]