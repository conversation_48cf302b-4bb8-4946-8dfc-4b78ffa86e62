using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.IdentityModel.Tokens;
using MMLib.Ocelot.Provider.AppConfiguration;
using Ocelot.Cache.CacheManager;
using Ocelot.DependencyInjection;
using Ocelot.Middleware;
using Ocelot.Provider.Polly;
using System.Security.Claims;
using System.Text.Json;

namespace OcelotApiGateway.Extensions
{
    public static class StartupConfiguration
    {
        public static WebApplicationBuilder ConfigureServices(this WebApplicationBuilder builder)
        {
            var logger = builder.Services.BuildServiceProvider().GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Configuring services for environment: {Environment}", builder.Environment.EnvironmentName);

            // Add authentication
            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer("Bearer", ConfigureJwtBearer(builder.Configuration, logger));

            // Add CORS
            builder.Services.AddCors(options =>
            {
                var allowedOrigins = builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ??
                    ["http://localhost:5002", "http://localhost:5003", "http://localhost:5130"];

                options.AddPolicy("CorsPolicy",
                    builder => builder
                        .WithOrigins(allowedOrigins)
                        .WithMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                        .WithHeaders("Authorization", "Content-Type", "Tenant-ID")
                        .SetIsOriginAllowed(origin =>
                            allowedOrigins.Contains(origin))
                        .AllowCredentials());
            });

            // Add health checks
            builder.Services.AddHealthChecks()
                .AddCheck("self", () => HealthCheckResult.Healthy());

            // Add controllers
            builder.Services.AddControllers();

            // Add Ocelot with resilience patterns
            builder.Services.AddOcelot(builder.Configuration)
                .AddAppConfiguration()
                .AddPolly()
                .AddCacheManager(x => x.WithDictionaryHandle());

            // Add Swagger for Ocelot
            builder.Services.AddSwaggerForOcelot(builder.Configuration);

            return builder;
        }

        public static WebApplication ConfigureMiddleware(this WebApplication app)
        {
            var logger = app.Services.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Configuring the HTTP request pipeline for environment: {Environment}", app.Environment.EnvironmentName);

            // Configure exception handling
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                logger.LogInformation("Developer exception page enabled");
            }
            else
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
                logger.LogInformation("Production error handling configured");
            }

            // Configure health checks
            app.UseHealthChecks("/health", new HealthCheckOptions
            {
                ResponseWriter = async (context, report) =>
                {
                    context.Response.ContentType = "application/json";
                    var result = JsonSerializer.Serialize(
                        new
                        {
                            status = report.Status.ToString(),
                            checks = report.Entries.Select(e => new
                            {
                                name = e.Key,
                                status = e.Value.Status.ToString(),
                                description = e.Value.Description
                            })
                        });
                    await context.Response.WriteAsync(result);
                }
            });
            logger.LogInformation("Health checks configured");

            // Configure routing and auth
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            logger.LogInformation("Authentication and authorization middleware configured");

            // Configure CORS
            app.UseCors("CorsPolicy");
            logger.LogInformation("CORS middleware configured");

            // Configure endpoints
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHealthChecks("/health");
                logger.LogInformation("Endpoints mapped");
            });

            // Configure Swagger UI
            app.UseSwaggerForOcelotUI(opt =>
            {
                opt.PathToSwaggerGenerator = "/swagger/docs";
                //opt.DocumentTitle = "API Gateway";
                logger.LogInformation("Swagger UI configured");
            });

            // Configure Ocelot
            try
            {
                app.UseOcelot().Wait();
                logger.LogInformation("Ocelot middleware configured");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to configure Ocelot middleware");
                throw;
            }

            return app;
        }

        private static Action<JwtBearerOptions> ConfigureJwtBearer(IConfiguration configuration, ILogger logger)
        {
            return options =>
            {
                var jwtSettings = configuration.GetSection("JwtSettings");
                var azureAd = configuration.GetSection("AzureAd");
                var securitySettings = configuration.GetSection("Security");

                options.Authority = $"{azureAd["Instance"]}{azureAd["TenantId"]}";
                options.Audience = azureAd["Audience"];
                options.RequireHttpsMetadata = securitySettings.GetValue<bool>("RequireHttps", false);

                // Configure token validation parameters
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = jwtSettings.GetValue<bool>("ValidateIssuer", true),
                    ValidateAudience = jwtSettings.GetValue<bool>("ValidateAudience", false),
                    ValidateLifetime = jwtSettings.GetValue<bool>("ValidateLifetime", true),
                    ValidateIssuerSigningKey = jwtSettings.GetValue<bool>("ValidateIssuerSigningKey", true),
                    ValidIssuer = jwtSettings["ValidIssuer"],
                    ValidAudiences = [azureAd["Audience"]],
                    ClockSkew = TimeSpan.Parse(jwtSettings["ClockSkew"] ?? "00:05:00"),
                    NameClaimType = "name",
                    RoleClaimType = "roles"
                };

                // Configure JWT events
                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        logger.LogWarning("Authentication failed: {Exception}", context.Exception);
                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context =>
                    {
                        logger.LogInformation("Token validated successfully");
                        ProcessTokenClaims(context, logger);
                        return Task.CompletedTask;
                    },
                    OnChallenge = context =>
                    {
                        logger.LogWarning("Challenge issued. Error: {Error}, ErrorDescription: {ErrorDescription}",
                            context.Error, context.ErrorDescription);
                        return Task.CompletedTask;
                    },
                    OnMessageReceived = context =>
                    {
                        logger.LogInformation("JWT token received: {Token}",
                            context.Token?.Substring(0, Math.Min(10, context.Token?.Length ?? 0)) + "...");
                        return Task.CompletedTask;
                    }
                };
            };
        }

        private static void ProcessTokenClaims(TokenValidatedContext context, ILogger logger)
        {
            if (context.Principal?.Identity is not ClaimsIdentity identity) return;

            // Log all claims in the token
            logger.LogInformation("Claims in the token:");
            foreach (var claim in identity.Claims)
            {
                logger.LogInformation("Claim: {type} = {value}", claim.Type, claim.Value);
            }

            // Add Admin role for users with specific email domains
            var email = identity.FindFirst(ClaimTypes.Email)?.Value
                ?? identity.FindFirst("preferred_username")?.Value
                ?? identity.FindFirst("email")?.Value;

            if (string.IsNullOrEmpty(email)) return;

            email = email.ToLowerInvariant();
            logger.LogInformation("User email: {Email}", email);

            // Add Admin role for specific users
            if (email == "<EMAIL>" || email == "<EMAIL>")
            {
                AddAdminRoleAndScopes(identity, logger);
            }

            // Add User role to all authenticated users
            AddUserRoleAndScopes(identity, logger);

            // Log the roles that were added
            LogRoles(identity, logger);
        }

        private static void AddAdminRoleAndScopes(ClaimsIdentity identity, ILogger logger)
        {
            logger.LogInformation("Adding Admin role based on email");
            if (!identity.HasClaim(c => c.Type == ClaimTypes.Role && c.Value == "Admin"))
            {
                identity.AddClaim(new Claim("role", "Admin"));
            }

            // Add required scopes for Ocelot AllowedScopes
            if (!identity.HasClaim(c => c.Type == "scope" && c.Value == "admin.read"))
            {
                logger.LogInformation("Adding admin.read scope for Ocelot AllowedScopes");
                identity.AddClaim(new Claim("scope", "admin.read"));
            }

            if (!identity.HasClaim(c => c.Type == "scope" && c.Value == "admin.write"))
            {
                logger.LogInformation("Adding admin.write scope for Ocelot AllowedScopes");
                identity.AddClaim(new Claim("scope", "admin.write"));
            }
        }

        private static void AddUserRoleAndScopes(ClaimsIdentity identity, ILogger logger)
        {
            // Add User role to all authenticated users
            if (!identity.HasClaim(c => c.Type == ClaimTypes.Role && c.Value == "User"))
            {
                logger.LogInformation("Adding User role to authenticated user");
                identity.AddClaim(new Claim("role", "User"));
            }

            // Add required scopes for Ocelot AllowedScopes
            if (!identity.HasClaim(c => c.Type == "scope" && c.Value == "cic.read"))
            {
                logger.LogInformation("Adding cic.read scope for Ocelot AllowedScopes");
                identity.AddClaim(new Claim("scope", "cic.read"));
            }

            if (!identity.HasClaim(c => c.Type == "scope" && c.Value == "cic.write"))
            {
                logger.LogInformation("Adding cic.write scope for Ocelot AllowedScopes");
                identity.AddClaim(new Claim("scope", "cic.write"));
            }
        }

        private static void LogRoles(ClaimsIdentity identity, ILogger logger)
        {
            logger.LogInformation("Roles after processing:");
            foreach (var claim in identity.Claims.Where(c => c.Type == ClaimTypes.Role))
            {
                logger.LogInformation("Role: {role}", claim.Value);
            }
        }
    }
}