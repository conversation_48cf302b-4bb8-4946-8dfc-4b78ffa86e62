﻿using Microsoft.Extensions.Configuration;

namespace CICPlatform.Admin.Application.Extensions
{
    public static class ApplicationExtensions
    {
        public static string IsValid(this IConfigurationSection configuration)
        {
            if (string.IsNullOrEmpty(configuration.Value))
                throw new Exception(configuration.Path);
            else
                return configuration.Value;
        }
    }
}