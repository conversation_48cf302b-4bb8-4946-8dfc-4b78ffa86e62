# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Admin/CICPlatform.Admin.API/CICPlatform.Admin.API.csproj", "src/Admin/CICPlatform.Admin.API/"]
COPY ["src/Admin/CICPlatform.Admin.Application/CICPlatform.Admin.Application.csproj", "src/Admin/CICPlatform.Admin.Application/"]
COPY ["src/Admin/CICPlatform.Admin.Domain/CICPlatform.Admin.Domain.csproj", "src/Admin/CICPlatform.Admin.Domain/"]
COPY ["src/Admin/CICPlatform.Admin.Infrastructure/CICPlatform.Admin.Infrastructure.csproj", "src/Admin/CICPlatform.Admin.Infrastructure/"]
COPY ["src/Admin/CICPlatform.Admin.Infrastructure.Persistence/CICPlatform.Admin.Infrastructure.Persistence.csproj", "src/Admin/CICPlatform.Admin.Infrastructure.Persistence/"]
RUN dotnet restore "./src/Admin/CICPlatform.Admin.API/CICPlatform.Admin.API.csproj"
COPY . .
WORKDIR "/src/src/Admin/CICPlatform.Admin.API"
RUN dotnet build "./CICPlatform.Admin.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./CICPlatform.Admin.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CICPlatform.Admin.API.dll"]