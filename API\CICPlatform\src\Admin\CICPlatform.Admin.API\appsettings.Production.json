{"ConnectionStrings": {"Default": "Server=10.20.4.88;Port=5432;Database=techskill;Username=techskill;Password=**************;Include Error Detail=true;", "Redis": "prod-redis-cluster:6379"}, "DatabaseSettings": {"DefaultDB": "PostgreSQL", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "CommandTimeout": 60, "MaxRetryCount": 3, "MaxRetryDelay": "00:01:00"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "pannapps.co", "TenantId": "93708259-3510-4cf8-bfab-b0802727f5e4", "ClientId": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "Audience": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "AudienceURI": "api://c0356a99-ac1b-4857-92db-1fcfc1c47d81", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "JwtSettings": {"ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ValidIssuer": "https://login.microsoftonline.com/93708259-3510-4cf8-bfab-b0802727f5e4/v2.0", "ClockSkew": "00:02:00"}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Error", "Microsoft.EntityFrameworkCore": "Warning", "CICPlatform": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Error", "System": "Error", "CICPlatform": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/var/log/admin-api/admin-api-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/var/log/admin-api/admin-api-errors-.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Error", "retainedFileCountLimit": 90, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithEnvironmentName"]}, "AllowedHosts": "*.yourdomain.com", "AllowedOrigins": ["https://yourdomain.com", "https://api.yourdomain.com"], "Caching": {"DefaultCacheExpiration": "01:00:00", "EnableDistributedCache": true, "CacheProvider": "Redis"}, "Security": {"RequireHttps": true, "EnableCors": true, "AllowCredentials": false, "EnableDetailedErrors": false, "EnableSensitiveDataLogging": false}, "Performance": {"EnableResponseCaching": true, "EnableCompression": true, "MaxRequestBodySize": 10485760}, "HealthChecks": {"Enabled": true, "DatabaseCheck": true, "ExternalServiceCheck": true}, "Development": {"EnableSwagger": false, "EnableDeveloperExceptionPage": false, "EnableSensitiveDataLogging": false, "BypassAuthentication": false, "MockExternalServices": false, "SeedTestData": false}, "Authorization": {"EnableRoleBasedAuthorization": true, "DefaultRole": "User", "AdminRole": "Admin", "RequireEmailVerification": true}, "ExternalServices": {"Timeout": "00:01:00", "RetryCount": 3, "RetryDelay": "00:00:05"}}