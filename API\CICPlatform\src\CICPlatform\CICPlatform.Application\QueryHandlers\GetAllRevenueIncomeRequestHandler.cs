﻿using AutoMapper;
using CICPlatform.Application.Contracts;
using CICPlatform.Application.DTOs;
using CICPlatform.Application.Queries;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Application.QueryHandlers
{
    public class GetAllRevenueIncomeRequestHandler(IUnitOfWork unitOfWork, IMapper mapper) : IRequestHandler<GetAllRevenueIncomeRequest, List<RevenueIncomeDto>>
    {
        public async Task<List<RevenueIncomeDto>> Handle(GetAllRevenueIncomeRequest request, CancellationToken cancellationToken)
        {
            return await unitOfWork.RevenueIncomeRepository.GetAllRevenueIncome();
        }
    }
}
