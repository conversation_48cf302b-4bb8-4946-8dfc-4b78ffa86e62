{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Ocelot": "Warning", "System.Net.Http.HttpClient": "Warning"}}, "AllowedHosts": "*.yourdomain.com", "AllowedOrigins": ["https://yourdomain.com", "https://api.yourdomain.com", "https://admin.yourdomain.com"], "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "pannapps.co", "TenantId": "93708259-3510-4cf8-bfab-b0802727f5e4", "ClientId": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "Audience": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "AudienceURI": "api://c0356a99-ac1b-4857-92db-1fcfc1c47d81", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "JwtSettings": {"ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ValidIssuer": "https://login.microsoftonline.com/93708259-3510-4cf8-bfab-b0802727f5e4/v2.0", "ClockSkew": "00:02:00"}, "Services": {"admin": {"DownstreamPath": "https://admin-api.yourdomain.com", "HealthCheckPath": "/health"}, "cic": {"DownstreamPath": "https://cic-api.yourdomain.com", "HealthCheckPath": "/health"}}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Error", "System": "Error", "Ocelot": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/var/log/ocelot/ocelot-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/var/log/ocelot/ocelot-errors-.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Error", "retainedFileCountLimit": 90, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithEnvironmentName"]}, "RateLimiting": {"ClientWhitelist": ["trusted-client"], "EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 1000}, "Resilience": {"ExceptionsAllowedBeforeBreaking": 3, "DurationOfBreak": 30000, "TimeoutValue": 30000}, "HealthChecks": {"Enabled": true, "Interval": "00:05:00", "Timeout": "00:01:00"}, "Security": {"RequireHttps": true, "EnableCors": true, "AllowCredentials": false, "EnableDetailedErrors": false}, "Performance": {"EnableResponseCaching": true, "EnableCompression": true, "MaxRequestBodySize": 10485760}, "Development": {"EnableSwagger": false, "EnableDeveloperExceptionPage": false, "EnableSensitiveDataLogging": false, "BypassAuthentication": false, "MockExternalServices": false}}