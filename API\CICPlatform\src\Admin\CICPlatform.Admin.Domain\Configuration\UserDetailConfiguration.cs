﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration
{
    public class UserDetailConfiguration : IEntityTypeConfiguration<UserDetail>, IEntityConfiguration
    {
        public void Configure(EntityTypeBuilder<UserDetail> builder)
        {
            builder.ToTable("UserDetail");
            builder.HasKey(c => c.UserDetailId);
            builder.Property(c => c.UserDetailId).ValueGeneratedOnAdd();
            builder.Property(c => c.MaximumLoginAttempts).IsRequired(false);
            builder.Property(c => c.LastLoginDate).IsRequired(false);
            builder.Property(c => c.LastLogoutDate).IsRequired(false);
            builder.Property(c => c.LastLoginIp).IsRequired(false).HasMaxLength(100);
            builder.Property(c => c.FavouriteMenu).IsRequired(false);
            builder.Property(c => c.FrequentMenu).IsRequired(false);
            builder.Property(c => c.UpdatedDate).IsRequired(false);

            builder.HasOne(a => a.User).WithOne().HasForeignKey<UserDetail>(a => a.UserId);
            builder.Navigation(e => e.User).AutoInclude();
            builder.Property(e => e.UserId)
                .HasAnnotation("DisplayName", "User")
                .HasAnnotation("AuditVirtualModel", "User")
                .HasAnnotation("DisplayMember", "UserName");

            builder.HasQueryFilter(c => c.User.Active != -1);
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new UserDetailConfiguration());
        }
    }
}