﻿using CICPlatform.Application.Contracts;
using CICPlatform.Domain.Craft;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Infrastructure.Persistence.Repositories
{
    public class RevenueIncomeTypeRepository(CICApplicationDbContext applicationDbContext) : GenericRepository<PFRevenueIncomeType>(applicationDbContext), IRevenueIncomeTypeRepository
    {
    }
}
