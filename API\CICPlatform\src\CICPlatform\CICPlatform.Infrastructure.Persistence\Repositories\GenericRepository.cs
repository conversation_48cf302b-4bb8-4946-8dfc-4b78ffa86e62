﻿using CICPlatform.Application.Contracts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CICPlatform.Infrastructure.Persistence.Repositories
{
    public class GenericRepository<T>(CICApplicationDbContext applicationDbContext) : IGenericRepository<T> where T : class
    {
        public async Task<T> Add(T entity)
        {
            await applicationDbContext.AddAsync(entity);
            return entity;
        }


    }
}
