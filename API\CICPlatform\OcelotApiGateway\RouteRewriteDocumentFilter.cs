﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace OcelotApiGateway
{
    public class RouteRewriteDocumentFilter : IDocumentFilter
    {
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            var updatedPaths = new OpenApiPaths();

            foreach (var path in swaggerDoc.Paths)
            {
                // Replace internal routes with gateway routes
                var newPath = path.Key
                    .Replace("/api/v1", "/gateway/v1");

                updatedPaths.Add(newPath, path.Value);
            }

            swaggerDoc.Paths = updatedPaths;
        }
    }
}