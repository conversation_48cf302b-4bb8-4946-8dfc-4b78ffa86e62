﻿using CICPlatform.Application.Commands;
using CICPlatform.Application.DTOs;
using CICPlatform.Application.Models;
using MediatR;
using System.Net;

namespace CICPlatform.API.EndpointDefinitions
{
    public static class RevenueIncomeTypeEndpointDefinition
    {
        public static void RegisterEndpoints(WebApplication app)
        {
            var RevenueIncomeTypeRouteGroup = app.MapGroup("/api/v1/revenues")
                .WithTags("revenues");
            RevenueIncomeTypeRouteGroup.MapPost("/", CreateRevenueIncomeType)
                .WithName("CreateRevenueIncomeType")
                .Produces<APIResponse>(200).Produces(400);
        }

        /// <summary>
        /// Create RevenueIncome Type
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="id"></param>
        /// <returns></returns>

        public static async Task<IResult> CreateRevenueIncomeType(IMediator mediator, CreateRevenueIncomeTypeDto createRevenueIncomeTypeDto)
        {
            var command = new CreateRevenueIncomeTypeCommand { CreateRevenueIncomeTypeDto = createRevenueIncomeTypeDto };
            APIResponse resultObj = new();
            var user = await mediator.Send(command);
            resultObj.IsSuccess = true;
            resultObj.Result = user;
            resultObj.StatusCode = HttpStatusCode.OK;

            return TypedResults.Ok(resultObj);
        }
    }
}
