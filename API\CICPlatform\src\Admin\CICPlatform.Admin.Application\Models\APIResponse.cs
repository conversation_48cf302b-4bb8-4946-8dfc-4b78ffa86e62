﻿using System.Net;

namespace CICPlatform.Admin.Application.Models
{
    public class APIResponse
    {
        public bool IsSuccess { get; set; }
        public object Result { get; set; }
        public int RecordCount { get; set; }
        public HttpStatusCode StatusCode { get; set; }
        public string TraceId { get; set; }
        public string Instance { get; set; }
        public List<string> ErrorMessage { get; set; }
        public Dictionary<string, List<CommonLookUp>> Collection { get; set; } = new Dictionary<string, List<CommonLookUp>>();

        public APIResponse()
        {
            ErrorMessage = new List<string>();
            Result = new List<object>();
            IsSuccess = false;
            StatusCode = HttpStatusCode.BadRequest;
            RecordCount = 0;
        }
    }
}