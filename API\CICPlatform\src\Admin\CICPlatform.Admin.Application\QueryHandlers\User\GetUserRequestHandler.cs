﻿using AutoMapper;
using CICPlatform.Admin.Application.Contracts;
using CICPlatform.Admin.Application.DTOs.User;
using CICPlatform.Admin.Application.Queries;
using MediatR;

namespace CICPlatform.Admin.Application.QueryHandlers.User
{
    public class GetUserRequestHandler(IUnitOfWork unitOfWork, IMapper mapper) : IRequestHandler<GetUserRequest, UserDTO>
    {
        public async Task<UserDTO> Handle(GetUserRequest request, CancellationToken cancellationToken)
        {
            var user = await unitOfWork.UserRepository.Get(request.Id);
            return mapper.Map<UserDTO>(user);
        }
    }
}