﻿using CICPlatform.Admin.Domain.Shared;
using CICPlatform.Admin.Domain.Shared.Contracts;
using CICPlatform.Admin.Domain.System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration
{
    public sealed class UserGroupMappingConfiguration(DbContextFilter contextFilter, bool feedSeedData = false) : AuditAttributesConfiguration<UserGroupMapping>(contextFilter), IEntityConfiguration
    {
        public UserGroupMappingConfiguration() : this(new DbContextFilter())
        {
        }

        public override void Configure(EntityTypeBuilder<UserGroupMapping> builder)
        {
            builder.ToTable("UserGroupMapping");
            builder.ToTable(t => t.HasComment("The purpose of this table is to map users to user groups."));
            builder.HasKey(e => e.UserGroupMappingId);
            builder.Property(e => e.UserGroupMappingId).ValueGeneratedNever();
            builder.Property(e => e.UserGroupId).IsRequired();
            builder.HasOne(e => e.UserGroup)
                   .WithMany(e => e.UserGroupMapping)
                   .HasForeignKey(e => e.UserGroupId);
            builder.Property(e => e.UserId)
                .IsRequired()
                .HasAnnotation("DisplayName", "User").HasComment("Users are mapped to the appropriate user group using this field.");
            builder.Property(e => e.DefaultUserGroup)
                .HasAnnotation("DisplayName", "Default User Group")
                .IsRequired(false)
                .HasComment("This field is used to indicate if the assigned group is the user's default group.");
            builder.Property(e => e.Active).HasAnnotation("DisplayName", "Active");
        }

        public void ConfigureEntity(ModelBuilder modelBuilder, DbContextFilter contextFilter, bool feedSeedData = false)
        {
            modelBuilder.ApplyConfiguration(new UserGroupMappingConfiguration(contextFilter, feedSeedData));
        }
    }
}