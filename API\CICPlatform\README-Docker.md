# CIC Platform Docker Setup

This document explains how to run the CIC Platform services using Docker, making them accessible independently of Visual Studio.

## Problem Solved

Previously, services were only accessible when running from Visual Studio. Now they can run independently in Docker containers with proper networking.

## Architecture

The platform consists of:
- **PostgreSQL Database** (port 5432) - Docker
- **Redis Cache** (port 6379) - Docker
- **Identity Server** (port 5130) - **Run from Visual Studio**
- **Admin API** (port 5002) - Docker
- **CIC API** (port 5003) - Docker
- **API Gateway** (Ocelot) (port 5004) - Docker

## Quick Start

### Option 1: Start All Services
```powershell
# Start all services
docker-compose up

# Start all services in background
docker-compose up -d

# Start all services with rebuild
docker-compose up --build
```

### Option 2: Use PowerShell Script
```powershell
# Start all services
.\start-services.ps1

# Start only infrastructure (DB, Redis)
.\start-services.ps1 -Services infrastructure

# Start only APIs
.\start-services.ps1 -Services apis

# Start only gateway
.\start-services.ps1 -Services gateway

# Start with rebuild and in background
.\start-services.ps1 -Build -Detached
```

## Access Points

Once running, access the services at:
- **API Gateway**: http://localhost:5004/swagger
- **Admin API**: http://localhost:5002/swagger
- **CIC API**: http://localhost:5003/swagger

## Service Dependencies

Services start in the correct order with health checks:
1. PostgreSQL and Redis (infrastructure)
2. Identity Server
3. Admin API and CIC API
4. API Gateway

## Development Workflow

### For Full Docker Development
```powershell
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

### For Mixed Development (some services in Docker, some in VS)
```powershell
# Start only infrastructure
docker-compose up postgres redis -d

# Then run your APIs from Visual Studio
# The APIs will connect to the containerized database and Redis
```

## Troubleshooting

### Services Not Accessible
- Ensure all containers are running: `docker-compose ps`
- Check container logs: `docker-compose logs [service-name]`
- Verify health checks: `docker-compose exec [service-name] curl http://localhost:[port]/health`

### Database Connection Issues
- Ensure PostgreSQL is running: `docker-compose logs postgres`
- Check connection string in environment variables

### Network Issues
- All services use the `cic-network` bridge network
- Services communicate using container names (e.g., `admin-api:5002`)

## Configuration

### Environment Variables
Key environment variables are set in `docker-compose.yml`:
- `ASPNETCORE_URLS`: Service binding URLs
- `ConnectionStrings__Default`: Database connection
- `ConnectionStrings__Redis`: Redis connection
- `Services__*__DownstreamPath`: Gateway routing

### Override for Development
Use `docker-compose.override.yml` for local development overrides.

## Cleanup

```powershell
# Stop and remove containers
docker-compose down

# Remove containers and volumes
docker-compose down -v

# Remove containers, volumes, and images
docker-compose down -v --rmi all
```
