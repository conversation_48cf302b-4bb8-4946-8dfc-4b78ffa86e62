{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "AllowedOrigins": ["http://localhost:5002", "http://localhost:5003", "http://localhost:5130", "http://localhost:4200", "http://localhost:4201"], "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "pannapps.co", "TenantId": "93708259-3510-4cf8-bfab-b0802727f5e4", "ClientId": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "Audience": "c0356a99-ac1b-4857-92db-1fcfc1c47d81", "AudienceURI": "api://c0356a99-ac1b-4857-92db-1fcfc1c47d81", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "JwtSettings": {"ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ValidIssuer": "https://login.microsoftonline.com/93708259-3510-4cf8-bfab-b0802727f5e4/v2.0"}, "IdentityServer": {"Authority": "http://localhost:5130"}, "Services": {"admin": {"DownstreamPath": "http://localhost:5002"}, "cic": {"DownstreamPath": "http://localhost:5003"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/ocelot-.txt", "rollingInterval": "Day"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "RateLimiting": {"ClientWhitelist": ["trusted-client"], "EnableRateLimiting": true, "Period": "1m", "PeriodTimespan": 60, "Limit": 10}, "Resilience": {"ExceptionsAllowedBeforeBreaking": 3, "DurationOfBreak": 10000, "TimeoutValue": 5000}}