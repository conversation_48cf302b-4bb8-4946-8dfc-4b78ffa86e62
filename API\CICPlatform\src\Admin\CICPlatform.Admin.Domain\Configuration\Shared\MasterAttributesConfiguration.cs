﻿using CICPlatform.Admin.Domain.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CICPlatform.Admin.Domain.Configuration.Shared
{
    public abstract class MasterAttributesConfiguration<TBase>(DbContextFilter contextFilter) : IEntityTypeConfiguration<TBase>
    where TBase : MasterAttributes
    {
        private readonly int businessUnitId = contextFilter.BusinessUnitId;

        void IEntityTypeConfiguration<TBase>.Configure(EntityTypeBuilder<TBase> builder)
        {
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).ValueGeneratedNever();
            builder.Property(e => e.Code).IsRequired().HasMaxLength(5);
            builder.Property(e => e.Description).IsRequired().HasMaxLength(50);

            builder.Property(e => e.Active)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Active");
            builder.Property(e => e.OriginBusinessUnitId)
                .IsRequired(false)
                .HasAnnotation("DisplayName", "Origin BusinessUnit")
                .HasAnnotation("Namespace", "Shared.Model")
                .HasAnnotation("AuditNonRelationalModel", "BusinessUnit")
                .HasAnnotation("DisplayMember", "Code-Name");

            builder.Property(e => e.SysSchemaId).IsRequired(false).HasAnnotation("DisplayName", "System SchemaId");
            builder.Property(e => e.CusSchemaId).IsRequired(false).HasAnnotation("DisplayName", "Custom SchemaId");
            builder.Property(e => e.SysJSON).IsRequired(false).HasAnnotation("DisplayName", "System Schema");
            builder.Property(e => e.CusJSON).IsRequired(false).HasAnnotation("DisplayName", "Custom Schema");
            builder.Property(e => e.ModelDefinitionId).IsRequired(false);
            builder.Property(e => e.IsSystemDefined).IsRequired(false);

            builder.HasOne(a => a.SysSchema).WithMany().HasForeignKey(a => a.SysSchemaId).OnDelete(DeleteBehavior.NoAction);
            builder.HasOne(a => a.CusSchema).WithMany().HasForeignKey(a => a.CusSchemaId).OnDelete(DeleteBehavior.NoAction);
            builder.Navigation(e => e.SysSchema).AutoInclude();
            builder.Navigation(e => e.CusSchema).AutoInclude();

            //---Global Filter
            builder.HasQueryFilter(c => c.Active != -1 /*&& c.OriginBusinessUnitId == this.businessUnitId*/);

            Configure(builder);
        }

        public abstract void Configure(EntityTypeBuilder<TBase> builder);
    }
}